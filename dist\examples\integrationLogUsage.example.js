"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.runExamples = exports.getStatsExample = exports.retrySyncExample = exports.queryLogsExample = exports.multipleEntitySyncExample = exports.singleEntitySyncExample = void 0;
const client_1 = require("@prisma/client");
const integrationLog_service_1 = require("@services/integrationLog.service");
const singleEntitySyncExample = async (companyId, userId) => {
    try {
        console.log('=== Single Entity Sync Example ===');
        const syncLog = await (0, integrationLog_service_1.createIntegrationLog)({
            companyId,
            userId,
            apiName: 'Invoices',
            method: 'GET',
            apiUrl: 'https://api.xero.com/api.xro/2.0/Invoices',
            entity: 'Invoices',
            syncType: client_1.SyncType.SINGLE_ENTITY,
            triggeredBy: 'USER',
            totalRecords: 150,
            apiRequest: {
                endpoint: '/Invoices',
                modifiedSince: '2024-07-01T00:00:00Z',
                pageSize: 100,
            },
        });
        console.log('Created sync log:', syncLog.Id);
        await (0, integrationLog_service_1.updateIntegrationLog)({
            id: syncLog.Id,
            syncStatus: client_1.SyncStatus.IN_PROGRESS,
            message: 'Fetching invoices from Xero API...',
            processedRecords: 0,
            progressPercentage: 0,
        });
        for (let processed = 0; processed < 150; processed += 50) {
            const currentBatch = Math.min(50, 150 - processed);
            const totalProcessed = processed + currentBatch;
            const progressPercentage = Math.round((totalProcessed / 150) * 100);
            await (0, integrationLog_service_1.updateIntegrationLog)({
                id: syncLog.Id,
                message: `Processing invoices: ${totalProcessed}/150`,
                processedRecords: totalProcessed,
                progressPercentage,
            });
            console.log(`Progress: ${progressPercentage}% (${totalProcessed}/150)`);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        await (0, integrationLog_service_1.updateIntegrationLog)({
            id: syncLog.Id,
            syncStatus: client_1.SyncStatus.SUCCESS,
            statusCode: '200',
            duration: '45s',
            message: 'Successfully synced 150 invoices',
            successCount: 150,
            failureCount: 0,
            apiResponse: {
                totalRecords: 150,
                processedRecords: 150,
                errors: [],
            },
        });
        console.log('Single entity sync completed successfully');
        return syncLog;
    }
    catch (error) {
        console.error('Single entity sync failed:', error);
        throw error;
    }
};
exports.singleEntitySyncExample = singleEntitySyncExample;
const multipleEntitySyncExample = async (companyId, userId) => {
    try {
        console.log('=== Multiple Entity Batch Sync Example ===');
        const entities = ['Accounts', 'Contacts', 'Invoices', 'Payments', 'Items'];
        const { parentLog, batchId } = await (0, integrationLog_service_1.createBatchSyncLog)(companyId, userId, entities, 'USER');
        console.log('Created batch sync log:', parentLog.Id, 'Batch ID:', batchId);
        const childLogs = await (0, integrationLog_service_1.createChildSyncLogs)(parentLog.Id, batchId, companyId, userId, entities);
        console.log('Created child logs:', childLogs.map(log => log.Id));
        const summary = {
            totalEntities: entities.length,
            successfulEntities: [],
            failedEntities: [],
            skippedEntities: [],
            totalRecords: 0,
            processedRecords: 0,
            errors: [],
        };
        for (let i = 0; i < childLogs.length; i++) {
            const childLog = childLogs[i];
            const entity = entities[i];
            try {
                await (0, integrationLog_service_1.updateIntegrationLog)({
                    id: childLog.Id,
                    syncStatus: client_1.SyncStatus.IN_PROGRESS,
                    message: `Syncing ${entity}...`,
                });
                const shouldFail = entity === 'Payments';
                const recordCount = Math.floor(Math.random() * 100) + 50;
                if (shouldFail) {
                    throw new Error(`Failed to sync ${entity}: API rate limit exceeded`);
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
                await (0, integrationLog_service_1.updateIntegrationLog)({
                    id: childLog.Id,
                    syncStatus: client_1.SyncStatus.SUCCESS,
                    statusCode: '200',
                    duration: '2s',
                    message: `Successfully synced ${recordCount} ${entity.toLowerCase()}`,
                    successCount: recordCount,
                    processedRecords: recordCount,
                    apiResponse: { recordCount, status: 'success' },
                });
                summary.successfulEntities.push(entity);
                summary.totalRecords += recordCount;
                summary.processedRecords += recordCount;
                console.log(`✓ ${entity} sync completed: ${recordCount} records`);
            }
            catch (error) {
                await (0, integrationLog_service_1.updateIntegrationLog)({
                    id: childLog.Id,
                    syncStatus: client_1.SyncStatus.ERROR,
                    statusCode: '429',
                    duration: '2s',
                    message: `Failed to sync ${entity}`,
                    failureCount: 1,
                    errorDetails: {
                        error: error instanceof Error ? error.message : 'Unknown error',
                        timestamp: new Date().toISOString(),
                    },
                });
                summary.failedEntities.push(entity);
                summary.errors.push({
                    entity,
                    error: error instanceof Error ? error.message : 'Unknown error',
                });
                console.log(`✗ ${entity} sync failed:`, error instanceof Error ? error.message : error);
            }
        }
        await (0, integrationLog_service_1.updateBatchSyncProgress)(parentLog.Id, summary);
        console.log('Batch sync completed with summary:', summary);
        return { parentLog, childLogs, summary };
    }
    catch (error) {
        console.error('Multiple entity sync failed:', error);
        throw error;
    }
};
exports.multipleEntitySyncExample = multipleEntitySyncExample;
const queryLogsExample = async (companyId, userId) => {
    try {
        console.log('=== Query Integration Logs Example ===');
        const allLogs = await (0, integrationLog_service_1.getIntegrationLogs)(companyId, userId, {
            limit: 10,
            offset: 0,
        });
        console.log(`Total logs: ${allLogs.pagination.totalCount}`);
        const failedLogs = await (0, integrationLog_service_1.getIntegrationLogs)(companyId, userId, {
            syncStatus: client_1.SyncStatus.ERROR,
            limit: 5,
        });
        console.log(`Failed logs: ${failedLogs.integrationLogs.length}`);
        const batchLogs = await (0, integrationLog_service_1.getIntegrationLogs)(companyId, userId, {
            syncType: client_1.SyncType.MULTIPLE_ENTITY,
            parentOnly: true,
            limit: 5,
        });
        console.log(`Batch sync logs: ${batchLogs.integrationLogs.length}`);
        const invoiceLogs = await (0, integrationLog_service_1.getIntegrationLogs)(companyId, userId, {
            entity: 'Invoices',
            limit: 5,
        });
        console.log(`Invoice sync logs: ${invoiceLogs.integrationLogs.length}`);
        const recentLogs = await (0, integrationLog_service_1.getIntegrationLogs)(companyId, userId, {
            startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
            endDate: new Date(),
            limit: 10,
        });
        console.log(`Recent logs (7 days): ${recentLogs.integrationLogs.length}`);
        return {
            allLogs: allLogs.pagination.totalCount,
            failedLogs: failedLogs.integrationLogs.length,
            batchLogs: batchLogs.integrationLogs.length,
            invoiceLogs: invoiceLogs.integrationLogs.length,
            recentLogs: recentLogs.integrationLogs.length,
        };
    }
    catch (error) {
        console.error('Query logs failed:', error);
        throw error;
    }
};
exports.queryLogsExample = queryLogsExample;
const retrySyncExample = async (failedLogId, userId) => {
    try {
        console.log('=== Retry Failed Sync Example ===');
        const retriedLog = await (0, integrationLog_service_1.retryIntegrationLog)(failedLogId, userId);
        console.log(`Retry initiated for log ${failedLogId}`);
        console.log(`Retry count: ${retriedLog.RetryCount}/${retriedLog.MaxRetries}`);
        console.log(`Status: ${retriedLog.SyncStatus}`);
        return retriedLog;
    }
    catch (error) {
        console.error('Retry sync failed:', error);
        throw error;
    }
};
exports.retrySyncExample = retrySyncExample;
const getStatsExample = async (companyId, userId) => {
    try {
        console.log('=== Integration Statistics Example ===');
        const stats = await (0, integrationLog_service_1.getIntegrationLogStats)(companyId, userId);
        console.log('Integration Log Statistics:');
        console.log(`Total logs: ${stats.totalLogs}`);
        console.log('Status distribution:', stats.statusDistribution);
        console.log('Sync type distribution:', stats.syncTypeDistribution);
        console.log(`Top entities: ${stats.entityDistribution.slice(0, 3).map(e => `${e.entity} (${e.count})`).join(', ')}`);
        console.log(`Recent logs: ${stats.recentLogs.length}`);
        const monthlyStats = await (0, integrationLog_service_1.getIntegrationLogStats)(companyId, userId, {
            startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
            endDate: new Date(),
        });
        console.log(`Monthly logs: ${monthlyStats.totalLogs}`);
        return { overallStats: stats, monthlyStats };
    }
    catch (error) {
        console.error('Get stats failed:', error);
        throw error;
    }
};
exports.getStatsExample = getStatsExample;
const runExamples = async () => {
    const companyId = 'your-company-id';
    const userId = 'your-user-id';
    try {
        console.log('All examples completed successfully');
    }
    catch (error) {
        console.error('Examples failed:', error);
    }
};
exports.runExamples = runExamples;
//# sourceMappingURL=integrationLogUsage.example.js.map