"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const integrationLog_controller_1 = require("@controllers/integrationLog.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/companies/:companyId/integration-logs', auth_middleware_1.authenticate, integrationLog_controller_1.getIntegrationLogsController);
router.get('/integration-logs/:logId', auth_middleware_1.authenticate, integrationLog_controller_1.getIntegrationLogDetailsController);
router.post('/integration-logs/:logId/retry', auth_middleware_1.authenticate, integrationLog_controller_1.retryIntegrationLogController);
router.get('/companies/:companyId/integration-stats', auth_middleware_1.authenticate, integrationLog_controller_1.getIntegrationStatsController);
exports.default = router;
//# sourceMappingURL=integrationLog.routes.js.map