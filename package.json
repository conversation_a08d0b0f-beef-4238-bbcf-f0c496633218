{"name": "furgal-backend", "version": "1.0.0", "description": "Backend API for Furgal application with Express, TypeScript, and Prisma", "main": "dist/server.js", "scripts": {"build": "tsc", "start": "node dist/server.js", "dev": "ts-node-dev --respawn --transpile-only --require tsconfig-paths/register src/server.ts", "dev:debug": "ts-node-dev --inspect --respawn --transpile-only src/server.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "format:check": "prettier --check src/**/*.ts", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset", "docker:build": "docker build -t furgal-backend .", "docker:run": "docker run -p 8000:8000 furgal-backend", "prepare": "husky install"}, "keywords": ["express", "typescript", "prisma", "postgresql", "rest-api", "backend"], "author": "Your Name <<EMAIL>>", "license": "MIT", "dependencies": {"@prisma/client": "^6.11.0", "axios": "^1.10.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^17.0.0", "express": "^5.1.0", "express-rate-limit": "^7.5.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "prisma": "^6.11.0", "uuid": "^11.1.0", "winston": "^3.17.0", "zod": "^3.25.67"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/compression": "^1.7.5", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.9", "@types/node": "^24.0.8", "@types/supertest": "^6.0.2", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.2", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/yourusername/furgal-backend.git"}, "bugs": {"url": "https://github.com/yourusername/furgal-backend/issues"}, "homepage": "https://github.com/yourusername/furgal-backend#readme", "lint-staged": {"*.{ts,js}": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm run test"}}}