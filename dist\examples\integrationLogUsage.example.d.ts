import { BatchSyncSummary } from '@services/integrationLog.service';
export declare const singleEntitySyncExample: (companyId: string, userId: string) => Promise<any>;
export declare const multipleEntitySyncExample: (companyId: string, userId: string) => Promise<{
    parentLog: any;
    childLogs: any;
    summary: BatchSyncSummary;
}>;
export declare const queryLogsExample: (companyId: string, userId: string) => Promise<{
    allLogs: number;
    failedLogs: number;
    batchLogs: number;
    invoiceLogs: number;
    recentLogs: number;
}>;
export declare const retrySyncExample: (failedLogId: string, userId: string) => Promise<any>;
export declare const getStatsExample: (companyId: string, userId: string) => Promise<{
    overallStats: any;
    monthlyStats: any;
}>;
export declare const runExamples: () => Promise<void>;
//# sourceMappingURL=integrationLogUsage.example.d.ts.map