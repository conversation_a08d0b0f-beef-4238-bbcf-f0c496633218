import winston from 'winston';
import { config } from '@config/config';

// Define custom logger type that includes <PERSON>'s Logger and custom methods
type CustomLogger = winston.Logger & {
  // Custom methods
  stream: {
    write(message: string): void;
  };
  logRequest(
    req: {
      method: string;
      originalUrl: string;
      user?: { id?: string };
      ip?: string;
      get(header: string): string | undefined;
    },
    res: { statusCode: number },
    responseTime: number
  ): void;
  logError(error: Error, context?: Record<string, unknown>): void;
  logSecurity(event: string, details: Record<string, unknown>): void;
};

/**
 * Winston logger configuration
 *
 * Supports:
 * - console transport
 * - optional file transports
 * - separate formats for development and production
 * - graceful exception/rejection handlers
 */

// Define the log format for development with colorized console output
const developmentFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.colorize(),
  winston.format.printf(({ timestamp, level, message, service, ...meta }) => {
    const metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : '';
    return `${timestamp} [${service}] ${level}: ${message} ${metaStr}`;
  })
);

// Define the log format for production with JSON output
const productionFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.printf((info) => {
    return JSON.stringify({
      ...info,
      hostname: process.env['HOSTNAME'] || 'unknown',
      pid: process.pid,
      environment: config.NODE_ENV,
    });
  })
);

// Setup transports based on environment
const transports: winston.transport[] = [];

// Always push console transport
transports.push(
  new winston.transports.Console({
    format: config.IS_DEVELOPMENT ? developmentFormat : productionFormat,
  })
);

// Optionally add file transports in production
if (config.IS_PRODUCTION) {
  if (config.LOGGING.ERROR_FILE) {
    transports.push(
      new winston.transports.File({
        filename: config.LOGGING.ERROR_FILE,
        level: 'error',
        format: productionFormat,
        maxsize: 5 * 1024 * 1024, // 5 MB
        maxFiles: 5,
      })
    );
  }
  if (config.LOGGING.COMBINED_FILE) {
    transports.push(
      new winston.transports.File({
        filename: config.LOGGING.COMBINED_FILE,
        format: productionFormat,
        maxsize: 5 * 1024 * 1024, // 5 MB
        maxFiles: 5,
      })
    );
  }
}

// Create the Winston logger instance
const logger = winston.createLogger({
  level: config.LOGGING.LEVEL,
  format: config.IS_PRODUCTION ? productionFormat : developmentFormat,
  defaultMeta: {
    service: 'furgal-backend',
    version: process.env['npm_package_version'] || '1.0.0',
  },
  transports,
  exceptionHandlers: [new winston.transports.File({ filename: 'logs/exceptions.log' })],
  rejectionHandlers: [new winston.transports.File({ filename: 'logs/rejections.log' })],
  exitOnError: false,
}) as CustomLogger;

/**
 * Provide a stream object compatible with Morgan middleware
 */
(logger as any).stream = {
  write: (message: string) => {
    logger.info(message.trim());
  },
};

/**
 * Structured request logger helper
 * Logs high-level HTTP request metadata
 */
(logger as any).logRequest = (
  req: {
    method: string;
    originalUrl: string;
    user?: { id?: string };
    ip?: string;
    get(header: string): string | undefined;
  },
  res: { statusCode: number },
  responseTime: number
) => {
  logger.info('HTTP Request', {
    method: req.method,
    url: req.originalUrl,
    statusCode: res.statusCode,
    responseTime: `${responseTime}ms`,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
  });
};

/**
 * Structured application error logging
 */
(logger as any).logError = (error: Error, context?: Record<string, unknown>) => {
  logger.error('Application Error', {
    message: error.message,
    stack: error.stack,
    name: error.name,
    ...context,
  });
};

/**
 * Structured security event logger
 */
(logger as any).logSecurity = (event: string, details: Record<string, unknown>) => {
  logger.warn('Security Event', {
    event,
    ...details,
    timestamp: new Date().toISOString(),
  });
};

export default logger as CustomLogger;
