import { SyncStatus } from '@prisma/client';
export interface CreateIntegrationLogRequest {
    requestId?: string;
    companyId: string;
    userId?: string;
    apiName: string;
    method?: string;
    apiUrl?: string;
    integrationName?: string;
    entity?: string;
    triggeredBy?: 'USER' | 'SYSTEM';
    maxRetries?: number;
    batchId?: string;
    parentLogId?: string;
    isParentLog?: boolean;
    totalRecords?: number;
    apiRequest?: any;
}
export interface UpdateIntegrationLogRequest {
    id: string;
    syncStatus?: SyncStatus;
    statusCode?: string;
    duration?: string;
    message?: string;
    entityCount?: number;
    successCount?: number;
    failureCount?: number;
    skippedCount?: number;
    processedRecords?: number;
    progressPercentage?: number;
    apiResponse?: any;
    errorDetails?: any;
    syncSummary?: any;
    completedAt?: Date;
}
export interface BatchSyncSummary {
    totalEntities: number;
    successfulEntities: string[];
    failedEntities: string[];
    skippedEntities: string[];
    totalRecords: number;
    processedRecords: number;
    errors: Array<{
        entity: string;
        error: string;
        details?: any;
    }>;
}
export declare const getIntegrationLogs: (companyId: string, userId: string, filters?: {
    entity?: string;
    syncStatus?: SyncStatus;
    integrationName?: string;
    startDate?: Date;
    endDate?: Date;
    batchId?: string;
    parentOnly?: boolean;
    limit?: number;
    offset?: number;
}) => Promise<{
    integrationLogs: {
        Id: string;
        CreatedAt: Date;
        UpdatedAt: Date;
        RequestId: string;
        CompanyId: string;
        ApiName: string;
        Method: string | null;
        ApiUrl: string | null;
        IntegrationName: string;
        StatusCode: string | null;
        Duration: string | null;
        Message: string | null;
        Entity: string | null;
        TriggeredBy: string | null;
        SyncStatus: import(".prisma/client").$Enums.SyncStatus;
        ApiRequest: import("@prisma/client/runtime/library").JsonValue | null;
        ApiResponse: import("@prisma/client/runtime/library").JsonValue | null;
        ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
        SyncSummary: import("@prisma/client/runtime/library").JsonValue | null;
        StartedAt: Date;
        CompletedAt: Date | null;
    }[];
    pagination: {
        totalCount: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
    filters: {
        entity?: string;
        syncStatus?: SyncStatus;
        integrationName?: string;
        startDate?: Date;
        endDate?: Date;
        batchId?: string;
        parentOnly?: boolean;
        limit?: number;
        offset?: number;
    };
}>;
//# sourceMappingURL=integrationLog.service.d.ts.map