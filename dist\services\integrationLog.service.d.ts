import { SyncStatus } from '@prisma/client';
export interface CreateIntegrationLogRequest {
    requestId?: string;
    companyId: string;
    userId?: string;
    apiName: string;
    method?: string;
    apiUrl?: string;
    integrationName?: string;
    entity?: string;
    triggeredBy?: 'USER' | 'SYSTEM';
    syncType?: 'SINGLE_ENTITY' | 'MULTIPLE_ENTITY' | 'FULL_SYNC';
    maxRetries?: number;
    batchId?: string;
    parentLogId?: string;
    isParentLog?: boolean;
    totalRecords?: number;
    apiRequest?: any;
}
export interface UpdateIntegrationLogRequest {
    id: string;
    syncStatus?: SyncStatus;
    statusCode?: string;
    duration?: string;
    message?: string;
    entityCount?: number;
    successCount?: number;
    failureCount?: number;
    skippedCount?: number;
    processedRecords?: number;
    progressPercentage?: number;
    apiResponse?: any;
    errorDetails?: any;
    syncSummary?: any;
    completedAt?: Date;
}
export interface BatchSyncSummary {
    totalEntities: number;
    successfulEntities: string[];
    failedEntities: string[];
    skippedEntities: string[];
    totalRecords: number;
    processedRecords: number;
    errors: Array<{
        entity: string;
        error: string;
        details?: any;
    }>;
}
export declare const getIntegrationLogs: (companyId: string, userId: string, filters?: {
    entity?: string;
    syncStatus?: SyncStatus;
    integrationName?: string;
    startDate?: Date;
    endDate?: Date;
    batchId?: string;
    parentOnly?: boolean;
    limit?: number;
    offset?: number;
}) => Promise<{
    integrationLogs: {
        Id: string;
        CreatedAt: Date;
        UpdatedAt: Date;
        userId: string | null;
        RequestId: string;
        CompanyId: string;
        ApiName: string;
        Method: string | null;
        ApiUrl: string | null;
        IntegrationName: string;
        StatusCode: string | null;
        Duration: string | null;
        Message: string | null;
        Entity: string | null;
        EntityCount: number | null;
        SuccessCount: number | null;
        FailureCount: number | null;
        SkippedCount: number | null;
        TriggeredBy: string | null;
        SyncType: import(".prisma/client").$Enums.SyncType;
        SyncStatus: import(".prisma/client").$Enums.SyncStatus;
        RetryCount: number;
        MaxRetries: number;
        LastRetryAt: Date | null;
        NextRetryAt: Date | null;
        BatchId: string | null;
        ParentLogId: string | null;
        IsParentLog: boolean;
        TotalRecords: number | null;
        ProcessedRecords: number | null;
        ProgressPercentage: import("@prisma/client/runtime/library").Decimal | null;
        ApiRequest: import("@prisma/client/runtime/library").JsonValue | null;
        ApiResponse: import("@prisma/client/runtime/library").JsonValue | null;
        ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
        SyncSummary: import("@prisma/client/runtime/library").JsonValue | null;
        StartedAt: Date;
        CompletedAt: Date | null;
    }[];
    pagination: {
        totalCount: number;
        limit: number;
        offset: number;
        hasMore: boolean;
    };
    filters: {
        entity?: string;
        syncStatus?: SyncStatus;
        integrationName?: string;
        startDate?: Date;
        endDate?: Date;
        batchId?: string;
        parentOnly?: boolean;
        limit?: number;
        offset?: number;
    };
}>;
export declare const createIntegrationLog: (data: CreateIntegrationLogRequest) => Promise<{
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    userId: string | null;
    RequestId: string;
    CompanyId: string;
    ApiName: string;
    Method: string | null;
    ApiUrl: string | null;
    IntegrationName: string;
    StatusCode: string | null;
    Duration: string | null;
    Message: string | null;
    Entity: string | null;
    EntityCount: number | null;
    SuccessCount: number | null;
    FailureCount: number | null;
    SkippedCount: number | null;
    TriggeredBy: string | null;
    SyncType: import(".prisma/client").$Enums.SyncType;
    SyncStatus: import(".prisma/client").$Enums.SyncStatus;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    BatchId: string | null;
    ParentLogId: string | null;
    IsParentLog: boolean;
    TotalRecords: number | null;
    ProcessedRecords: number | null;
    ProgressPercentage: import("@prisma/client/runtime/library").Decimal | null;
    ApiRequest: import("@prisma/client/runtime/library").JsonValue | null;
    ApiResponse: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
    SyncSummary: import("@prisma/client/runtime/library").JsonValue | null;
    StartedAt: Date;
    CompletedAt: Date | null;
}>;
export declare const updateIntegrationLog: (data: UpdateIntegrationLogRequest) => Promise<{
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    userId: string | null;
    RequestId: string;
    CompanyId: string;
    ApiName: string;
    Method: string | null;
    ApiUrl: string | null;
    IntegrationName: string;
    StatusCode: string | null;
    Duration: string | null;
    Message: string | null;
    Entity: string | null;
    EntityCount: number | null;
    SuccessCount: number | null;
    FailureCount: number | null;
    SkippedCount: number | null;
    TriggeredBy: string | null;
    SyncType: import(".prisma/client").$Enums.SyncType;
    SyncStatus: import(".prisma/client").$Enums.SyncStatus;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    BatchId: string | null;
    ParentLogId: string | null;
    IsParentLog: boolean;
    TotalRecords: number | null;
    ProcessedRecords: number | null;
    ProgressPercentage: import("@prisma/client/runtime/library").Decimal | null;
    ApiRequest: import("@prisma/client/runtime/library").JsonValue | null;
    ApiResponse: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
    SyncSummary: import("@prisma/client/runtime/library").JsonValue | null;
    StartedAt: Date;
    CompletedAt: Date | null;
}>;
export declare const getIntegrationLogStats: (companyId: string, userId: string) => Promise<{
    totalLogs: number;
    successLogs: number;
    errorLogs: number;
    pendingLogs: number;
    successRate: number;
}>;
export declare const retryIntegrationLog: (logId: string, userId: string) => Promise<{
    Id: string;
    CreatedAt: Date;
    UpdatedAt: Date;
    userId: string | null;
    RequestId: string;
    CompanyId: string;
    ApiName: string;
    Method: string | null;
    ApiUrl: string | null;
    IntegrationName: string;
    StatusCode: string | null;
    Duration: string | null;
    Message: string | null;
    Entity: string | null;
    EntityCount: number | null;
    SuccessCount: number | null;
    FailureCount: number | null;
    SkippedCount: number | null;
    TriggeredBy: string | null;
    SyncType: import(".prisma/client").$Enums.SyncType;
    SyncStatus: import(".prisma/client").$Enums.SyncStatus;
    RetryCount: number;
    MaxRetries: number;
    LastRetryAt: Date | null;
    NextRetryAt: Date | null;
    BatchId: string | null;
    ParentLogId: string | null;
    IsParentLog: boolean;
    TotalRecords: number | null;
    ProcessedRecords: number | null;
    ProgressPercentage: import("@prisma/client/runtime/library").Decimal | null;
    ApiRequest: import("@prisma/client/runtime/library").JsonValue | null;
    ApiResponse: import("@prisma/client/runtime/library").JsonValue | null;
    ErrorDetails: import("@prisma/client/runtime/library").JsonValue | null;
    SyncSummary: import("@prisma/client/runtime/library").JsonValue | null;
    StartedAt: Date;
    CompletedAt: Date | null;
}>;
//# sourceMappingURL=integrationLog.service.d.ts.map