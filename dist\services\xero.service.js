"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.reconnectCompanyXero = exports.disconnectCompanyXero = exports.getCompanyById = exports.getCompaniesWithFilters = exports.refreshXeroTokens = exports.disconnectXero = exports.handleXeroCallback = exports.getXeroAuthUrl = void 0;
const config_1 = require("@config/config");
const axios_1 = __importDefault(require("axios"));
const qs_1 = __importDefault(require("qs"));
const logger_1 = __importDefault(require("@utils/logger"));
const error_middleware_1 = require("@middlewares/error.middleware");
const getXeroAuthUrl = () => {
    const scope = [
        'openid',
        'profile',
        'email',
        'offline_access',
        'accounting.transactions',
        'accounting.settings',
        'accounting.contacts',
        'accounting.reports.read',
    ].join(' ');
    const authUrl = `${config_1.config.XERO.XERO_AUTH_URL}?${qs_1.default.stringify({
        response_type: 'code',
        client_id: config_1.config.XERO.XERO_CLIENT_ID,
        redirect_uri: config_1.config.XERO.XERO_REDIRECT_URI,
        scope,
        state: 'companyId-placeholder',
    })}`;
    return authUrl;
};
exports.getXeroAuthUrl = getXeroAuthUrl;
const handleXeroCallback = async (code, userId) => {
    try {
        const tokenResponse = await axios_1.default.post(config_1.config.XERO.XERO_TOKEN_URL, qs_1.default.stringify({
            grant_type: 'authorization_code',
            code,
            redirect_uri: config_1.config.XERO.XERO_REDIRECT_URI,
            client_id: config_1.config.XERO.XERO_CLIENT_ID,
            client_secret: config_1.config.XERO.XERO_CLIENT_SECRET,
        }), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });
        const { access_token, refresh_token, expires_in } = tokenResponse.data;
        logger_1.default.debug('Xero token exchange completed successfully', {
            accessTokenLength: access_token.length,
            refreshTokenLength: refresh_token.length,
            expiresIn: expires_in,
        });
        const tenantResponse = await axios_1.default.get('https://api.xero.com/connections', {
            headers: {
                Authorization: `Bearer ${access_token}`,
                Accept: 'application/json',
            },
        });
        const tenants = tenantResponse.data;
        if (!tenants || tenants.length === 0) {
            throw new error_middleware_1.XeroUnauthorizedError('No active Xero tenant found for this account.', 'XERO_NO_TENANT');
        }
        const tenant = tenants[0];
        let financialYearEnd = null;
        try {
            const orgResponse = await axios_1.default.get(`https://api.xero.com/api.xro/2.0/Organisation`, {
                headers: {
                    Authorization: `Bearer ${access_token}`,
                    'Xero-tenant-id': tenant.tenantId,
                    Accept: 'application/json',
                },
            });
            const organisations = orgResponse.data?.Organisations;
            if (organisations && organisations.length > 0) {
                const org = organisations[0];
                console.log("org", org);
                if (org.FinancialYearEndDay && org.FinancialYearEndMonth) {
                    const day = String(org.FinancialYearEndDay).padStart(2, '0');
                    const month = String(org.FinancialYearEndMonth).padStart(2, '0');
                    financialYearEnd = `${month}/${day}`;
                }
                logger_1.default.debug('Fetched organization financial year details', {
                    tenantId: tenant.tenantId,
                    financialYearEndDay: org.FinancialYearEndDay,
                    financialYearEndMonth: org.FinancialYearEndMonth,
                    formattedFinancialYearEnd: financialYearEnd,
                });
            }
        }
        catch (orgError) {
            logger_1.default.warn('Failed to fetch organization details for financial year', {
                tenantId: tenant.tenantId,
                error: orgError.message,
            });
        }
        const existingCompany = await config_1.prisma.company.findFirst({
            where: {
                XeroTenantId: tenant.tenantId,
            },
        });
        let updatedCompany;
        if (existingCompany) {
            updatedCompany = await config_1.prisma.company.update({
                where: { Id: existingCompany.Id },
                data: {
                    XeroAccessToken: access_token,
                    XeroRefreshToken: refresh_token,
                    XeroTenantId: tenant.tenantId,
                    Name: tenant.tenantName,
                    XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
                    XeroRefreshTokenExpiry: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
                    ConnectionStatus: 'ACTIVE',
                    FinancialYearEnd: financialYearEnd,
                },
            });
        }
        else {
            updatedCompany = await config_1.prisma.company.create({
                data: {
                    Name: tenant.tenantName,
                    XeroTenantId: tenant.tenantId,
                    XeroAccessToken: access_token,
                    XeroRefreshToken: refresh_token,
                    XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
                    UserId: userId,
                    XeroRefreshTokenExpiry: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
                    ConnectionStatus: 'ACTIVE',
                    FinancialYearEnd: financialYearEnd,
                },
            });
        }
        return updatedCompany;
    }
    catch (error) {
        logger_1.default.error('Error in handleXeroCallback', {
            error: error.message,
            stack: error.stack,
            userId,
            code: `${code?.substring(0, 10)}...`,
        });
        throw new error_middleware_1.UnauthorizedError(`Failed to complete Xero auth flow: ${error.message}`, 'XERO_CALLBACK_FAILED');
    }
};
exports.handleXeroCallback = handleXeroCallback;
const disconnectXero = async (companyId) => {
    const company = await config_1.prisma.company.findUnique({
        where: { Id: companyId },
    });
    if (!company || !company.XeroAccessToken) {
        throw new error_middleware_1.UnauthorizedError('Company has no active Xero connection to disconnect.', 'NO_XERO_CONNECTION');
    }
    try {
        await axios_1.default.post('https://identity.xero.com/connect/revocation', qs_1.default.stringify({
            token: company.XeroRefreshToken,
            client_id: config_1.config.XERO.XERO_CLIENT_ID,
            client_secret: config_1.config.XERO.XERO_CLIENT_SECRET,
        }), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });
        await config_1.prisma.company.update({
            where: { Id: companyId },
            data: {
                XeroAccessToken: null,
                XeroRefreshToken: null,
                XeroTokenExpiry: null,
                XeroRefreshTokenExpiry: null,
                ConnectionStatus: 'DISCONNECTED',
            },
        });
    }
    catch (error) {
        logger_1.default.error('Xero disconnect error', {
            error: error.message,
            stack: error.stack,
            companyId,
        });
        throw new error_middleware_1.UnauthorizedError('Failed to disconnect Xero account.', 'XERO_DISCONNECT_FAILED');
    }
};
exports.disconnectXero = disconnectXero;
const refreshXeroTokens = async (companyId) => {
    const company = await config_1.prisma.company.findUnique({
        where: { Id: companyId },
    });
    if (!company?.XeroRefreshToken) {
        throw new error_middleware_1.BadRequestError('No Xero refresh token found for this company.', 'NO_REFRESH_TOKEN');
    }
    try {
        const response = await axios_1.default.post(config_1.config.XERO.XERO_TOKEN_URL, qs_1.default.stringify({
            grant_type: 'refresh_token',
            refresh_token: company.XeroRefreshToken,
            client_id: config_1.config.XERO.XERO_CLIENT_ID,
            client_secret: config_1.config.XERO.XERO_CLIENT_SECRET,
        }), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
        });
        const { access_token, refresh_token, expires_in } = response.data;
        const updatedCompany = await config_1.prisma.company.update({
            where: { Id: companyId },
            data: {
                XeroAccessToken: access_token,
                XeroRefreshToken: refresh_token,
                XeroTokenExpiry: new Date(Date.now() + expires_in * 1000),
            },
        });
        return updatedCompany;
    }
    catch (error) {
        throw new error_middleware_1.UnauthorizedError(`Failed to refresh Xero token: ${error.message}`, 'XERO_REFRESH_FAILED');
    }
};
exports.refreshXeroTokens = refreshXeroTokens;
const getCompaniesWithFilters = async (userId, filters = {}) => {
    try {
        const { name, connectionStatus, hasXeroConnection, createdAfter, createdBefore, limit = 10, offset = 0, sortBy = 'createdAt', sortOrder = 'desc', } = filters;
        const whereClause = {
            UserId: userId,
        };
        if (name) {
            whereClause.Name = {
                contains: name,
                mode: 'insensitive',
            };
        }
        if (connectionStatus) {
            whereClause.ConnectionStatus = connectionStatus;
        }
        if (hasXeroConnection !== undefined) {
            if (hasXeroConnection) {
                whereClause.XeroTenantId = {
                    not: null,
                };
                whereClause.XeroAccessToken = {
                    not: null,
                };
            }
            else {
                whereClause.OR = [{ XeroTenantId: null }, { XeroAccessToken: null }];
            }
        }
        if (createdAfter || createdBefore) {
            whereClause.CreatedAt = {};
            if (createdAfter) {
                whereClause.CreatedAt.gte = createdAfter;
            }
            if (createdBefore) {
                whereClause.CreatedAt.lte = createdBefore;
            }
        }
        const orderBy = {};
        if (sortBy === 'name') {
            orderBy.Name = sortOrder;
        }
        else if (sortBy === 'updatedAt') {
            orderBy.UpdatedAt = sortOrder;
        }
        else {
            orderBy.CreatedAt = sortOrder;
        }
        const [companies, totalCount] = await Promise.all([
            config_1.prisma.company.findMany({
                where: whereClause,
                orderBy,
                take: Math.min(limit, 100),
                skip: offset,
                select: {
                    Id: true,
                    Name: true,
                    XeroTenantId: true,
                    ConnectionStatus: true,
                    FinancialYearEnd: true,
                    CreatedAt: true,
                    UpdatedAt: true,
                    XeroTokenExpiry: true,
                },
            }),
            config_1.prisma.company.count({
                where: whereClause,
            }),
        ]);
        const totalPages = Math.ceil(totalCount / limit);
        const currentPage = Math.floor(offset / limit) + 1;
        const hasNextPage = offset + limit < totalCount;
        const hasPreviousPage = offset > 0;
        logger_1.default.info('Companies retrieved successfully', {
            userId,
            totalCount,
            returnedCount: companies.length,
            filters,
        });
        return {
            companies,
            pagination: {
                totalCount,
                totalPages,
                currentPage,
                limit,
                offset,
                hasNextPage,
                hasPreviousPage,
            },
            filters,
        };
    }
    catch (error) {
        logger_1.default.error('Failed to retrieve companies', {
            userId,
            filters,
            error: error.message,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retrieve companies: ${error.message}`, 'COMPANY_RETRIEVAL_FAILED');
    }
};
exports.getCompaniesWithFilters = getCompaniesWithFilters;
const getCompanyById = async (userId, companyId) => {
    try {
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
            select: {
                Id: true,
                Name: true,
                XeroTenantId: true,
                ConnectionStatus: true,
                FinancialYearEnd: true,
                CreatedAt: true,
                UpdatedAt: true,
                XeroTokenExpiry: true,
                XeroRefreshTokenExpiry: true,
                User: {
                    select: {
                        Id: true,
                        Email: true,
                        Name: true,
                    },
                },
            },
        });
        if (!company) {
            logger_1.default.warn('Company not found or access denied', {
                userId,
                companyId,
            });
            return null;
        }
        logger_1.default.info('Company retrieved successfully', {
            userId,
            companyId: company.Id,
            companyName: company.Name,
        });
        return company;
    }
    catch (error) {
        logger_1.default.error('Failed to retrieve company', {
            userId,
            companyId,
            error: error.message,
        });
        throw new error_middleware_1.BadRequestError(`Failed to retrieve company: ${error.message}`, 'COMPANY_RETRIEVAL_FAILED');
    }
};
exports.getCompanyById = getCompanyById;
const disconnectCompanyXero = async (userId, companyId) => {
    try {
        const existingCompany = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!existingCompany) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        if (!existingCompany.XeroTenantId && !existingCompany.XeroAccessToken) {
            throw new error_middleware_1.BadRequestError('Company does not have an active Xero connection', 'NO_XERO_CONNECTION');
        }
        const updatedCompany = await config_1.prisma.company.update({
            where: {
                Id: companyId,
            },
            data: {
                XeroAccessToken: null,
                XeroRefreshToken: null,
                XeroTokenExpiry: null,
                XeroRefreshTokenExpiry: null,
                ConnectionStatus: 'DISCONNECTED',
                UpdatedAt: new Date(),
            },
            select: {
                Id: true,
                Name: true,
                XeroTenantId: true,
                ConnectionStatus: true,
                FinancialYearEnd: true,
                CreatedAt: true,
                UpdatedAt: true,
                XeroTokenExpiry: true,
            },
        });
        logger_1.default.info('Company Xero connection disconnected successfully', {
            userId,
            companyId: updatedCompany.Id,
            companyName: updatedCompany.Name,
            previousStatus: existingCompany.ConnectionStatus,
            newStatus: updatedCompany.ConnectionStatus,
        });
        return updatedCompany;
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        logger_1.default.error('Failed to disconnect company Xero connection', {
            userId,
            companyId,
            error: error.message,
        });
        throw new error_middleware_1.BadRequestError(`Failed to disconnect Xero connection: ${error.message}`, 'XERO_DISCONNECT_FAILED');
    }
};
exports.disconnectCompanyXero = disconnectCompanyXero;
const reconnectCompanyXero = async (userId, companyId) => {
    try {
        const existingCompany = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
            select: {
                Id: true,
                Name: true,
                ConnectionStatus: true,
                XeroTenantId: true,
                XeroAccessToken: true,
                XeroRefreshToken: true,
            },
        });
        if (!existingCompany) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        if (existingCompany.ConnectionStatus === 'ACTIVE' && existingCompany.XeroAccessToken) {
            throw new error_middleware_1.BadRequestError('Company already has an active Xero connection. Disconnect first if you want to reconnect.', 'ALREADY_CONNECTED');
        }
        await config_1.prisma.company.update({
            where: {
                Id: companyId,
            },
            data: {
                ConnectionStatus: 'PENDING',
                UpdatedAt: new Date(),
            },
        });
        const authUrl = (0, exports.getXeroAuthUrl)().replace('companyId-placeholder', companyId);
        logger_1.default.info('Company Xero reconnection initiated', {
            userId,
            companyId: existingCompany.Id,
            companyName: existingCompany.Name,
            previousStatus: existingCompany.ConnectionStatus,
            newStatus: 'PENDING',
        });
        return {
            authorizationUrl: authUrl,
            companyId,
            companyName: existingCompany.Name,
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        logger_1.default.error('Failed to initiate company Xero reconnection', {
            userId,
            companyId,
            error: error.message,
        });
        throw new error_middleware_1.BadRequestError(`Failed to initiate Xero reconnection: ${error.message}`, 'XERO_RECONNECT_FAILED');
    }
};
exports.reconnectCompanyXero = reconnectCompanyXero;
//# sourceMappingURL=xero.service.js.map