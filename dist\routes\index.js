"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const user_routes_1 = __importDefault(require("@routes/user.routes"));
const xero_routes_1 = __importDefault(require("@routes/xero.routes"));
const company_routes_1 = __importDefault(require("@routes/company.routes"));
const sync_routes_1 = __importDefault(require("@routes/sync.routes"));
const integrationLog_routes_1 = __importDefault(require("@routes/integrationLog.routes"));
const xeroModuleSync_routes_1 = __importDefault(require("@routes/xeroModuleSync.routes"));
const router = (0, express_1.Router)();
router.use('/user', user_routes_1.default);
router.use('/xero', xero_routes_1.default);
router.use('/companies', company_routes_1.default);
router.use('/sync', sync_routes_1.default);
router.use('/integration', integrationLog_routes_1.default);
router.use('/syncXero', xeroModuleSync_routes_1.default);
exports.default = router;
//# sourceMappingURL=index.js.map