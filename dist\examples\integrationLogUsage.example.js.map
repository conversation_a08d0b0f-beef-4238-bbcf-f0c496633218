{"version": 3, "file": "integrationLogUsage.example.js", "sourceRoot": "", "sources": ["../../src/examples/integrationLogUsage.example.ts"], "names": [], "mappings": ";;;AAiBA,2CAAsD;AACtD,6EAU0C;AAOnC,MAAM,uBAAuB,GAAG,KAAK,EAC1C,SAAiB,EACjB,MAAc,EACd,EAAE;IACF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;QAGlD,MAAM,OAAO,GAAG,MAAM,IAAA,6CAAoB,EAAC;YACzC,SAAS;YACT,MAAM;YACN,OAAO,EAAE,UAAU;YACnB,MAAM,EAAE,KAAK;YACb,MAAM,EAAE,2CAA2C;YACnD,MAAM,EAAE,UAAU;YAClB,QAAQ,EAAE,iBAAQ,CAAC,aAAa;YAChC,WAAW,EAAE,MAAM;YACnB,YAAY,EAAE,GAAG;YACjB,UAAU,EAAE;gBACV,QAAQ,EAAE,WAAW;gBACrB,aAAa,EAAE,sBAAsB;gBACrC,QAAQ,EAAE,GAAG;aACd;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,OAAO,CAAC,EAAE,CAAC,CAAC;QAG7C,MAAM,IAAA,6CAAoB,EAAC;YACzB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,UAAU,EAAE,mBAAU,CAAC,WAAW;YAClC,OAAO,EAAE,oCAAoC;YAC7C,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,CAAC;SACtB,CAAC,CAAC;QAGH,KAAK,IAAI,SAAS,GAAG,CAAC,EAAE,SAAS,GAAG,GAAG,EAAE,SAAS,IAAI,EAAE,EAAE,CAAC;YACzD,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,GAAG,SAAS,CAAC,CAAC;YACnD,MAAM,cAAc,GAAG,SAAS,GAAG,YAAY,CAAC;YAChD,MAAM,kBAAkB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,cAAc,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,CAAC;YAEpE,MAAM,IAAA,6CAAoB,EAAC;gBACzB,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,OAAO,EAAE,wBAAwB,cAAc,MAAM;gBACrD,gBAAgB,EAAE,cAAc;gBAChC,kBAAkB;aACnB,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,aAAa,kBAAkB,MAAM,cAAc,OAAO,CAAC,CAAC;YAGxE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;QAC1D,CAAC;QAGD,MAAM,IAAA,6CAAoB,EAAC;YACzB,EAAE,EAAE,OAAO,CAAC,EAAE;YACd,UAAU,EAAE,mBAAU,CAAC,OAAO;YAC9B,UAAU,EAAE,KAAK;YACjB,QAAQ,EAAE,KAAK;YACf,OAAO,EAAE,kCAAkC;YAC3C,YAAY,EAAE,GAAG;YACjB,YAAY,EAAE,CAAC;YACf,WAAW,EAAE;gBACX,YAAY,EAAE,GAAG;gBACjB,gBAAgB,EAAE,GAAG;gBACrB,MAAM,EAAE,EAAE;aACX;SACF,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;QACzD,OAAO,OAAO,CAAC;IAEjB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACnD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA9EW,QAAA,uBAAuB,2BA8ElC;AAMK,MAAM,yBAAyB,GAAG,KAAK,EAC5C,SAAiB,EACjB,MAAc,EACd,EAAE;IACF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;QAE1D,MAAM,QAAQ,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAG3E,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,IAAA,2CAAkB,EACrD,SAAS,EACT,MAAM,EACN,QAAQ,EACR,MAAM,CACP,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,SAAS,CAAC,EAAE,EAAE,WAAW,EAAE,OAAO,CAAC,CAAC;QAG3E,MAAM,SAAS,GAAG,MAAM,IAAA,4CAAmB,EACzC,SAAS,CAAC,EAAE,EACZ,OAAO,EACP,SAAS,EACT,MAAM,EACN,QAAQ,CACT,CAAC;QAEF,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QAGjE,MAAM,OAAO,GAAqB;YAChC,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,kBAAkB,EAAE,EAAE;YACtB,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,EAAE;YACnB,YAAY,EAAE,CAAC;YACf,gBAAgB,EAAE,CAAC;YACnB,MAAM,EAAE,EAAE;SACX,CAAC;QAEF,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC1C,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAC9B,MAAM,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE3B,IAAI,CAAC;gBAEH,MAAM,IAAA,6CAAoB,EAAC;oBACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,UAAU,EAAE,mBAAU,CAAC,WAAW;oBAClC,OAAO,EAAE,WAAW,MAAM,KAAK;iBAChC,CAAC,CAAC;gBAGH,MAAM,UAAU,GAAG,MAAM,KAAK,UAAU,CAAC;gBACzC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,GAAG,CAAC,GAAG,EAAE,CAAC;gBAEzD,IAAI,UAAU,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,kBAAkB,MAAM,2BAA2B,CAAC,CAAC;gBACvE,CAAC;gBAGD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC;gBAGxD,MAAM,IAAA,6CAAoB,EAAC;oBACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,UAAU,EAAE,mBAAU,CAAC,OAAO;oBAC9B,UAAU,EAAE,KAAK;oBACjB,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,uBAAuB,WAAW,IAAI,MAAM,CAAC,WAAW,EAAE,EAAE;oBACrE,YAAY,EAAE,WAAW;oBACzB,gBAAgB,EAAE,WAAW;oBAC7B,WAAW,EAAE,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE;iBAChD,CAAC,CAAC;gBAEH,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACxC,OAAO,CAAC,YAAY,IAAI,WAAW,CAAC;gBACpC,OAAO,CAAC,gBAAgB,IAAI,WAAW,CAAC;gBAExC,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,oBAAoB,WAAW,UAAU,CAAC,CAAC;YAEpE,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAEf,MAAM,IAAA,6CAAoB,EAAC;oBACzB,EAAE,EAAE,QAAQ,CAAC,EAAE;oBACf,UAAU,EAAE,mBAAU,CAAC,KAAK;oBAC5B,UAAU,EAAE,KAAK;oBACjB,QAAQ,EAAE,IAAI;oBACd,OAAO,EAAE,kBAAkB,MAAM,EAAE;oBACnC,YAAY,EAAE,CAAC;oBACf,YAAY,EAAE;wBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;wBAC/D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACpC;iBACF,CAAC,CAAC;gBAEH,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACpC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;oBAClB,MAAM;oBACN,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe;iBAChE,CAAC,CAAC;gBAEH,OAAO,CAAC,GAAG,CAAC,KAAK,MAAM,eAAe,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;YAC1F,CAAC;QACH,CAAC;QAGD,MAAM,IAAA,gDAAuB,EAAC,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;QAErD,OAAO,CAAC,GAAG,CAAC,oCAAoC,EAAE,OAAO,CAAC,CAAC;QAC3D,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;IAE3C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AArHW,QAAA,yBAAyB,6BAqHpC;AAMK,MAAM,gBAAgB,GAAG,KAAK,EACnC,SAAiB,EACjB,MAAc,EACd,EAAE;IACF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAGtD,MAAM,OAAO,GAAG,MAAM,IAAA,2CAAkB,EAAC,SAAS,EAAE,MAAM,EAAE;YAC1D,KAAK,EAAE,EAAE;YACT,MAAM,EAAE,CAAC;SACV,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,eAAe,OAAO,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;QAG5D,MAAM,UAAU,GAAG,MAAM,IAAA,2CAAkB,EAAC,SAAS,EAAE,MAAM,EAAE;YAC7D,UAAU,EAAE,mBAAU,CAAC,KAAK;YAC5B,KAAK,EAAE,CAAC;SACT,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QAGjE,MAAM,SAAS,GAAG,MAAM,IAAA,2CAAkB,EAAC,SAAS,EAAE,MAAM,EAAE;YAC5D,QAAQ,EAAE,iBAAQ,CAAC,eAAe;YAClC,UAAU,EAAE,IAAI;YAChB,KAAK,EAAE,CAAC;SACT,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,oBAAoB,SAAS,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QAGpE,MAAM,WAAW,GAAG,MAAM,IAAA,2CAAkB,EAAC,SAAS,EAAE,MAAM,EAAE;YAC9D,MAAM,EAAE,UAAU;YAClB,KAAK,EAAE,CAAC;SACT,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,sBAAsB,WAAW,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QAGxE,MAAM,UAAU,GAAG,MAAM,IAAA,2CAAkB,EAAC,SAAS,EAAE,MAAM,EAAE;YAC7D,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YACzD,OAAO,EAAE,IAAI,IAAI,EAAE;YACnB,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,yBAAyB,UAAU,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,CAAC;QAE1E,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,UAAU,CAAC,UAAU;YACtC,UAAU,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;YAC7C,SAAS,EAAE,SAAS,CAAC,eAAe,CAAC,MAAM;YAC3C,WAAW,EAAE,WAAW,CAAC,eAAe,CAAC,MAAM;YAC/C,UAAU,EAAE,UAAU,CAAC,eAAe,CAAC,MAAM;SAC9C,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAC3C,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAxDW,QAAA,gBAAgB,oBAwD3B;AAMK,MAAM,gBAAgB,GAAG,KAAK,EACnC,WAAmB,EACnB,MAAc,EACd,EAAE;IACF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAGjD,MAAM,UAAU,GAAG,MAAM,IAAA,4CAAmB,EAAC,WAAW,EAAE,MAAM,CAAC,CAAC;QAElE,OAAO,CAAC,GAAG,CAAC,2BAA2B,WAAW,EAAE,CAAC,CAAC;QACtD,OAAO,CAAC,GAAG,CAAC,gBAAgB,UAAU,CAAC,UAAU,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;QAC9E,OAAO,CAAC,GAAG,CAAC,WAAW,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;QAEhD,OAAO,UAAU,CAAC;IAEpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;QAC3C,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AApBW,QAAA,gBAAgB,oBAoB3B;AAMK,MAAM,eAAe,GAAG,KAAK,EAClC,SAAiB,EACjB,MAAc,EACd,EAAE;IACF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;QAGtD,MAAM,KAAK,GAAG,MAAM,IAAA,+CAAsB,EAAC,SAAS,EAAE,MAAM,CAAC,CAAC;QAE9D,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;QAC3C,OAAO,CAAC,GAAG,CAAC,eAAe,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC;QAC9D,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,KAAK,CAAC,oBAAoB,CAAC,CAAC;QACnE,OAAO,CAAC,GAAG,CAAC,iBAAiB,KAAK,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACrH,OAAO,CAAC,GAAG,CAAC,gBAAgB,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;QAGvD,MAAM,YAAY,GAAG,MAAM,IAAA,+CAAsB,EAAC,SAAS,EAAE,MAAM,EAAE;YACnE,SAAS,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAC1D,OAAO,EAAE,IAAI,IAAI,EAAE;SACpB,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,iBAAiB,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;QAEvD,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC;IAE/C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAC1C,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AA/BW,QAAA,eAAe,mBA+B1B;AAMK,MAAM,WAAW,GAAG,KAAK,IAAI,EAAE;IACpC,MAAM,SAAS,GAAG,iBAAiB,CAAC;IACpC,MAAM,MAAM,GAAG,cAAc,CAAC;IAE9B,IAAI,CAAC;QAaH,OAAO,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;IACrD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IAC3C,CAAC;AACH,CAAC,CAAC;AArBW,QAAA,WAAW,eAqBtB"}