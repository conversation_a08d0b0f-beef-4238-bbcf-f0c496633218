/**
 * @fileoverview Integration Log Routes
 * @description Express routes for integration logging and monitoring functionality.
 * Provides endpoints for viewing sync history, detailed logs, retry operations,
 * and sync statistics using the new IntegrationLog system.
 *
 * Routes:
 * - GET /companies/:companyId/integration-logs - Get paginated integration logs with filtering
 * - GET /integration-logs/:logId - Get detailed integration log by ID
 * - POST /integration-logs/:logId/retry - Retry a failed integration operation
 * - GET /companies/:companyId/integration-stats - Get integration statistics for a company
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-07-08
 */

import { Router } from 'express';
import {
  getIntegrationLogsController,
  getIntegrationLogDetailsController,
  retryIntegrationLogController,
  getIntegrationStatsController,
} from '@controllers/integrationLog.controller';
import { authenticate } from '@middlewares/auth.middleware';

const router = Router();

/**
 * @route GET /api/v1/companies/:companyId/integration-logs
 * @desc Get integration logs for a company with filtering and pagination
 * @access Private (requires authentication)
 * @param {string} companyId - Company UUID
 * @query {string} [startDate] - Filter logs from this date (ISO string)
 * @query {string} [endDate] - Filter logs until this date (ISO string)
 * @query {string} [entity] - Filter by entity type (Accounts, Invoices, etc.)
 * @query {string} [syncStatus] - Filter by sync status (PENDING, SUCCESS, ERROR, etc.)
 * @query {string} [syncType] - Filter by sync type (SINGLE_ENTITY, MULTIPLE_ENTITY, etc.)
 * @query {string} [integrationName] - Filter by integration name (Xero, QuickBooks, etc.)
 * @query {boolean} [parentOnly] - Show only parent logs for batch operations
 * @query {number} [limit=50] - Number of results per page (1-100)
 * @query {number} [offset=0] - Number of results to skip
 * @example
 * GET /api/v1/companies/123e4567-e89b-12d3-a456-************/integration-logs?entity=Accounts&syncStatus=SUCCESS&limit=20
 * @returns {Object} 200 - Success response with integration logs
 * @returns {Object} 400 - Bad request (invalid parameters)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Company not found
 * @returns {Object} 500 - Internal server error
 */
router.get('/companies/:companyId/integration-logs', authenticate, getIntegrationLogsController);

/**
 * @route GET /api/v1/integration-logs/:logId
 * @desc Get detailed information about a specific integration log
 * @access Private (requires authentication)
 * @param {string} logId - Integration log UUID
 * @example
 * GET /api/v1/integration-logs/123e4567-e89b-12d3-a456-************
 * @returns {Object} 200 - Success response with detailed integration log
 * @returns {Object} 400 - Bad request (invalid log ID)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Integration log not found
 * @returns {Object} 500 - Internal server error
 */
router.get('/integration-logs/:logId', authenticate, getIntegrationLogDetailsController);

/**
 * @route POST /api/v1/integration-logs/:logId/retry
 * @desc Manually retry a failed integration operation
 * @access Private (requires authentication)
 * @param {string} logId - Integration log UUID to retry
 * @example
 * POST /api/v1/integration-logs/123e4567-e89b-12d3-a456-************/retry
 * @returns {Object} 200 - Success response with retry confirmation
 * @returns {Object} 400 - Bad request (invalid log ID or retry not allowed)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Integration log not found
 * @returns {Object} 409 - Conflict (sync in progress or max retries exceeded)
 * @returns {Object} 500 - Internal server error
 */
router.post('/integration-logs/:logId/retry', authenticate, retryIntegrationLogController);

/**
 * @route GET /api/v1/companies/:companyId/integration-stats
 * @desc Get aggregated integration statistics for a company
 * @access Private (requires authentication)
 * @param {string} companyId - Company UUID
 * @query {string} [startDate] - Filter stats from this date (ISO string)
 * @query {string} [endDate] - Filter stats until this date (ISO string)
 * @example
 * GET /api/v1/companies/123e4567-e89b-12d3-a456-************/integration-stats
 * @returns {Object} 200 - Success response with integration statistics
 * @returns {Object} 400 - Bad request (invalid company ID)
 * @returns {Object} 401 - Unauthorized
 * @returns {Object} 404 - Company not found
 * @returns {Object} 500 - Internal server error
 */
router.get('/companies/:companyId/integration-stats', authenticate, getIntegrationStatsController);

export default router;
