import { Request, Response, NextFunction } from 'express';
interface AuthenticatedRequest extends Request {
    user?: {
        id: string;
        email: string;
        name?: string;
    };
}
export declare const getIntegrationLogsController: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getIntegrationLogDetailsController: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const retryIntegrationLogController: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export declare const getIntegrationStatsController: (req: AuthenticatedRequest, res: Response, next: NextFunction) => Promise<void>;
export {};
//# sourceMappingURL=integrationLog.controller.d.ts.map