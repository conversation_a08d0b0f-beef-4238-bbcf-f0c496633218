"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("tsconfig-paths/register");
const cluster_1 = __importDefault(require("cluster"));
const os_1 = __importDefault(require("os"));
const app_1 = __importDefault(require("./app"));
const logger_1 = __importDefault(require("./utils/logger"));
const config_1 = require("@config/config");
const gracefulShutdown = async (server, signal) => {
    logger_1.default.info(`Received ${signal}. Starting graceful shutdown...`);
    server.close(async (err) => {
        if (err) {
            logger_1.default.error('Error during server shutdown:', err);
            process.exit(1);
        }
        try {
            await config_1.prisma.$disconnect();
            logger_1.default.info('Database connections closed');
            logger_1.default.info('Graceful shutdown completed');
            process.exit(0);
        }
        catch (error) {
            logger_1.default.error('Error during graceful shutdown:', error);
            process.exit(1);
        }
    });
    setTimeout(() => {
        logger_1.default.error('Forced shutdown after timeout');
        process.exit(1);
    }, 30000);
};
const startServer = () => {
    const server = app_1.default.listen(config_1.config.PORT, config_1.config.HOST, () => {
        logger_1.default.info(`🚀 Worker ${process.pid} running on http://${config_1.config.HOST}:${config_1.config.PORT}`);
        logger_1.default.info(`Environment: ${config_1.config.NODE_ENV}`);
        logger_1.default.info(`API Base URL: http://${config_1.config.HOST}:${config_1.config.PORT}${config_1.config.API.PREFIX}/${config_1.config.API.VERSION}`);
    });
    server.on('error', (error) => {
        if (error.syscall !== 'listen') {
            throw error;
        }
        const bind = typeof config_1.config.PORT === 'string' ? `Pipe ${config_1.config.PORT}` : `Port ${config_1.config.PORT}`;
        switch (error.code) {
            case 'EACCES':
                logger_1.default.error(`${bind} requires elevated privileges`);
                process.exit(1);
            case 'EADDRINUSE':
                logger_1.default.error(`${bind} is already in use`);
                process.exit(1);
            default:
                throw error;
        }
    });
    process.on('SIGTERM', () => gracefulShutdown(server, 'SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown(server, 'SIGINT'));
    process.on('uncaughtException', (error) => {
        logger_1.default.error('Uncaught Exception:', error);
        gracefulShutdown(server, 'UNCAUGHT_EXCEPTION');
    });
    process.on('unhandledRejection', (reason, promise) => {
        logger_1.default.error('Unhandled Rejection at:', promise, 'reason:', reason);
        gracefulShutdown(server, 'UNHANDLED_REJECTION');
    });
    return server;
};
const shouldUseCluster = config_1.config.IS_PRODUCTION && !process.env['DISABLE_CLUSTER'];
const numCPUs = os_1.default.cpus().length;
if (shouldUseCluster && cluster_1.default.isPrimary) {
    logger_1.default.info(`Primary process ${process.pid} is running`);
    logger_1.default.info(`Forking ${numCPUs} workers...`);
    for (let i = 0; i < numCPUs; i++) {
        cluster_1.default.fork();
    }
    cluster_1.default.on('exit', (worker, code, signal) => {
        logger_1.default.warn(`Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
        cluster_1.default.fork();
    });
    process.on('SIGTERM', () => {
        logger_1.default.info('Primary received SIGTERM, shutting down workers...');
        for (const id in cluster_1.default.workers) {
            cluster_1.default.workers[id]?.kill();
        }
    });
}
else {
    startServer();
}
//# sourceMappingURL=server.js.map