"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createModule = exports.getModuleSyncStatus = exports.XERO_MODULES = void 0;
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
exports.XERO_MODULES = [
    'Accounts',
    'Bank Transactions',
    'Bank Transfers',
    'Budgets',
    'Contacts',
    'Credit Notes',
    'Currencies',
    'Employees',
    'Expense Claims',
    'Invoices',
    'Journals',
    'Manual Journals',
    'Payments',
    'Tracking Categories',
    'Tax Rates',
    'Attachments',
    'Reports (P&L, BS, TB)',
];
const getModuleSyncStatus = async (companyId) => {
    try {
        logger_1.default.debug('Fetching module sync status', { companyId });
        const syncRecords = await config_1.prisma.xeroModuleSync.findMany({
            where: { CompanyId: companyId },
            orderBy: { ModuleName: 'asc' },
            select: {
                Id: true,
                CompanyId: true,
                ModuleName: true,
                LastSyncTime: true,
                CreatedAt: true,
                UpdatedAt: true,
            }
        });
        return syncRecords.map(record => ({
            id: record.Id,
            companyId: record.CompanyId,
            moduleName: record.ModuleName,
            lastSyncTime: record.LastSyncTime,
            createdAt: record.CreatedAt,
            updatedAt: record.UpdatedAt,
        }));
    }
    catch (error) {
        logger_1.default.error('Error fetching module sync status', {
            companyId,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to fetch module sync status: ${error.message}`);
    }
};
exports.getModuleSyncStatus = getModuleSyncStatus;
const createModule = async (companyId) => {
    try {
        logger_1.default.debug('Creating module sync records', { companyId });
        await config_1.prisma.xeroModuleSync.createMany({
            data: exports.XERO_MODULES.map(moduleName => ({
                CompanyId: companyId,
                ModuleName: moduleName,
            })),
        });
    }
    catch (error) {
        logger_1.default.error('Error creating module sync records', {
            companyId,
            error: error.message,
            stack: error.stack,
        });
        throw new Error(`Failed to create module sync records: ${error.message}`);
    }
};
exports.createModule = createModule;
//# sourceMappingURL=xeroModuleSync.service.js.map