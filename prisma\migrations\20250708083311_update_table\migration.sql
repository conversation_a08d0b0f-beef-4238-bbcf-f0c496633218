/*
  Warnings:

  - You are about to drop the column `BatchId` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `EntityCount` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `FailureCount` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `IsParentLog` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `LastRetryAt` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `MaxRetries` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `NextRetryAt` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `ParentLogId` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `ProcessedRecords` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `ProgressPercentage` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `RetryCount` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `SkippedCount` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `SuccessCount` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `SyncType` on the `IntegrationLog` table. All the data in the column will be lost.
  - You are about to drop the column `TotalRecords` on the `IntegrationLog` table. All the data in the column will be lost.

*/
-- DropForeignKey
ALTER TABLE "IntegrationLog" DROP CONSTRAINT "IntegrationLog_ParentLogId_fkey";

-- DropIndex
DROP INDEX "IntegrationLog_BatchId_idx";

-- DropIndex
DROP INDEX "IntegrationLog_ParentLogId_idx";

-- AlterTable
ALTER TABLE "IntegrationLog" DROP COLUMN "BatchId",
DROP COLUMN "EntityCount",
DROP COLUMN "FailureCount",
DROP COLUMN "IsParentLog",
DROP COLUMN "LastRetryAt",
DROP COLUMN "MaxRetries",
DROP COLUMN "NextRetryAt",
DROP COLUMN "ParentLogId",
DROP COLUMN "ProcessedRecords",
DROP COLUMN "ProgressPercentage",
DROP COLUMN "RetryCount",
DROP COLUMN "SkippedCount",
DROP COLUMN "SuccessCount",
DROP COLUMN "SyncType",
DROP COLUMN "TotalRecords";

-- DropEnum
DROP TYPE "SyncType";
