export declare const SYNC_ENTITIES: readonly ["Accounts", "BankTransactions", "BankTransfers", "Budgets", "Contacts", "CreditNotes", "Currencies", "Employees", "ExpenseClaims", "Invoices", "Journals", "ManualJournals", "Items", "Payments", "PurchaseOrders", "TaxRates", "TrackingCategories", "Attachments", "TrialBalance", "ProfitLoss", "BalanceSheet"];
export type SyncEntity = (typeof SYNC_ENTITIES)[number];
export interface SyncTriggerRequest {
    entities: SyncEntity[];
    companyId: string;
    priority?: "HIGH" | "NORMAL" | "LOW";
    fullSync?: boolean;
}
export declare const getSyncStatus: (userId: string, companyId: string) => Promise<{
    companyId: string;
    companyName: string;
    connectionStatus: import(".prisma/client").$Enums.ConnectionStatus;
    entities: {
        entity: "Accounts" | "BalanceSheet" | "BankTransactions" | "ProfitLoss" | "TrialBalance" | "Employees" | "Invoices" | "Journals" | "Payments" | "Attachments" | "BankTransfers" | "Budgets" | "Contacts" | "CreditNotes" | "Currencies" | "ExpenseClaims" | "ManualJournals" | "Items" | "PurchaseOrders" | "TaxRates" | "TrackingCategories";
        lastSync: Date | null;
        status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "NEVER_SYNCED";
        errorMessage: string;
    }[];
    lastUpdated: Date;
}>;
export declare const triggerEntitySync: (userId: string, request: SyncTriggerRequest) => Promise<{
    success: boolean;
    message: string;
    data: {
        companyId: string;
        companyName: string;
        triggerRecords: any[];
        estimatedDuration: string;
    };
}>;
export declare const triggerAllEntitiesSync: (userId: string, companyId: string, options?: {
    priority?: "HIGH" | "NORMAL" | "LOW";
    fullSync?: boolean;
}) => Promise<{
    success: boolean;
    message: string;
    data: {
        companyId: string;
        companyName: string;
        triggerRecords: any[];
        estimatedDuration: string;
    };
}>;
export declare const getSyncHistory: (userId: string, companyId: string, options?: {
    limit?: number;
    offset?: number;
    entity?: SyncEntity;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
}) => Promise<{
    companyId: string;
    companyName: string;
    history: {
        id: any;
        entity: any;
        status: any;
        errorMessage: any;
        duration: any;
        createdAt: any;
        completedAt: any;
    }[];
    pagination: {
        totalCount: number;
        totalPages: number;
        currentPage: number;
        limit: number;
        offset: number;
        hasNextPage: boolean;
        hasPreviousPage: boolean;
    };
}>;
//# sourceMappingURL=sync.service.d.ts.map