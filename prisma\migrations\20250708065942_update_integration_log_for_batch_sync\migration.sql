/*
  Warnings:

  - You are about to drop the `ApiLog` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `SyncLog` table. If the table is not empty, all the data it contains will be lost.

*/
-- CreateEnum
CREATE TYPE "SyncType" AS ENUM ('SINGLE_ENTITY', 'MULTIPLE_ENTITY', 'FULL_SYNC', 'INCREMENTAL_SYNC');

-- DropForeignKey
ALTER TABLE "ApiLog" DROP CONSTRAINT "ApiLog_CompanyId_fkey";

-- DropForeignKey
ALTER TABLE "ApiLog" DROP CONSTRAINT "ApiLog_UserId_fkey";

-- DropForeignKey
ALTER TABLE "SyncLog" DROP CONSTRAINT "SyncLog_CompanyId_fkey";

-- DropForeignKey
ALTER TABLE "SyncLog" DROP CONSTRAINT "SyncLog_UserId_fkey";

-- DropTable
DROP TABLE "ApiLog";

-- DropTable
DROP TABLE "SyncLog";

-- CreateTable
CREATE TABLE "IntegrationLog" (
    "Id" UUID NOT NULL,
    "RequestId" VARCHAR(100) NOT NULL,
    "CompanyId" UUID NOT NULL,
    "ApiName" VARCHAR(100) NOT NULL,
    "Method" VARCHAR(10),
    "ApiUrl" VARCHAR(500),
    "IntegrationName" VARCHAR(100) NOT NULL DEFAULT 'Xero',
    "StatusCode" VARCHAR(20),
    "Duration" VARCHAR(20),
    "Message" TEXT,
    "Entity" VARCHAR(100),
    "EntityCount" INTEGER,
    "SuccessCount" INTEGER,
    "FailureCount" INTEGER,
    "SkippedCount" INTEGER,
    "TriggeredBy" VARCHAR(20),
    "SyncType" "SyncType" NOT NULL DEFAULT 'SINGLE_ENTITY',
    "SyncStatus" "SyncStatus" NOT NULL DEFAULT 'PENDING',
    "RetryCount" INTEGER NOT NULL DEFAULT 0,
    "MaxRetries" INTEGER NOT NULL DEFAULT 3,
    "LastRetryAt" TIMESTAMP(3),
    "NextRetryAt" TIMESTAMP(3),
    "BatchId" VARCHAR(100),
    "ParentLogId" UUID,
    "IsParentLog" BOOLEAN NOT NULL DEFAULT false,
    "TotalRecords" INTEGER,
    "ProcessedRecords" INTEGER,
    "ProgressPercentage" DECIMAL(5,2),
    "ApiRequest" JSONB,
    "ApiResponse" JSONB,
    "ErrorDetails" JSONB,
    "SyncSummary" JSONB,
    "StartedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "CompletedAt" TIMESTAMP(3),
    "CreatedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "UpdatedAt" TIMESTAMP(3) NOT NULL,
    "userId" UUID,

    CONSTRAINT "IntegrationLog_pkey" PRIMARY KEY ("Id")
);

-- CreateIndex
CREATE INDEX "IntegrationLog_CompanyId_SyncStatus_idx" ON "IntegrationLog"("CompanyId", "SyncStatus");

-- CreateIndex
CREATE INDEX "IntegrationLog_RequestId_idx" ON "IntegrationLog"("RequestId");

-- CreateIndex
CREATE INDEX "IntegrationLog_BatchId_idx" ON "IntegrationLog"("BatchId");

-- CreateIndex
CREATE INDEX "IntegrationLog_ParentLogId_idx" ON "IntegrationLog"("ParentLogId");

-- CreateIndex
CREATE INDEX "IntegrationLog_CreatedAt_idx" ON "IntegrationLog"("CreatedAt");

-- AddForeignKey
ALTER TABLE "IntegrationLog" ADD CONSTRAINT "IntegrationLog_CompanyId_fkey" FOREIGN KEY ("CompanyId") REFERENCES "Company"("Id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "IntegrationLog" ADD CONSTRAINT "IntegrationLog_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("Id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "IntegrationLog" ADD CONSTRAINT "IntegrationLog_ParentLogId_fkey" FOREIGN KEY ("ParentLogId") REFERENCES "IntegrationLog"("Id") ON DELETE SET NULL ON UPDATE CASCADE;
