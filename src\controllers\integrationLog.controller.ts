/**
 * @fileoverview Integration Log Controller
 * @description Controller for managing integration operation logs and monitoring.
 * Provides endpoints for retrieving integration history, viewing detailed logs,
 * and managing retry operations for failed integrations.
 *
 * Key Features:
 * - Integration history retrieval with filtering
 * - Detailed integration log viewing
 * - Manual retry functionality
 * - Real-time integration monitoring
 * - Performance analytics and statistics
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-07-08
 */

import { Request, Response, NextFunction } from 'express';
import { SyncStatus, SyncType } from '@prisma/client';
import {
  getIntegrationLogs,
  getIntegrationLogStats,
  retryIntegrationLog,
} from '@services/integrationLog.service';
import { prisma } from '@config/config';
import logger from '@utils/logger';
import { BadRequestError } from '@middlewares/error.middleware';

/**
 * Interface for authenticated request with user information
 */
interface AuthenticatedRequest extends Request {
  user?: {
    id: string;
    email: string;
    name?: string;
  };
}

/**
 * Controller: Get integration logs with filtering and pagination
 * Returns paginated list of integration operations for a company
 *
 * Query Parameters:
 * - startDate: Filter logs from this date (ISO string)
 * - endDate: Filter logs until this date (ISO string)
 * - entity: Filter by entity type (Accounts, Invoices, etc.)
 * - syncStatus: Filter by sync status (PENDING, SUCCESS, ERROR, etc.)
 * - syncType: Filter by sync type (SINGLE_ENTITY, MULTIPLE_ENTITY, etc.)
 * - integrationName: Filter by integration name (Xero, QuickBooks, etc.)
 * - parentOnly: Show only parent logs for batch operations (boolean)
 * - limit: Number of results per page (1-100, default: 50)
 * - offset: Number of results to skip (default: 0)
 *
 * @example
 * GET /api/v1/companies/{companyId}/integration-logs?entity=Accounts&syncStatus=SUCCESS&limit=20
 */
export const getIntegrationLogsController = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const userId = req.user?.id;


    if (!userId) {
      throw new BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
    }

    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'COMPANY_ID_REQUIRED');
    }

    // Parse and validate query parameters
    const {
      startDate,
      endDate,
      entity,
      syncStatus,
      syncType,
      integrationName,
      parentOnly,
      limit = '50',
      offset = '0',
    } = req.query;

    // Validate and parse dates
    let parsedStartDate: Date | undefined;
    let parsedEndDate: Date | undefined;

    if (startDate) {
      parsedStartDate = new Date(startDate as string);
      if (isNaN(parsedStartDate.getTime())) {
        throw new BadRequestError('Invalid start date format', 'INVALID_START_DATE');
      }
    }

    if (endDate) {
      parsedEndDate = new Date(endDate as string);
      if (isNaN(parsedEndDate.getTime())) {
        throw new BadRequestError('Invalid end date format', 'INVALID_END_DATE');
      }
    }

    // Validate sync status
    if (syncStatus && !Object.values(SyncStatus).includes(syncStatus as SyncStatus)) {
      throw new BadRequestError('Invalid sync status', 'INVALID_SYNC_STATUS');
    }

    // Validate sync type
    if (syncType && !Object.values(SyncType).includes(syncType as SyncType)) {
      throw new BadRequestError('Invalid sync type', 'INVALID_SYNC_TYPE');
    }

    // Validate pagination parameters
    const parsedLimit = parseInt(limit as string, 10);
    const parsedOffset = parseInt(offset as string, 10);

    if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 100) {
      throw new BadRequestError('Limit must be between 1 and 100', 'INVALID_LIMIT');
    }

    if (isNaN(parsedOffset) || parsedOffset < 0) {
      throw new BadRequestError('Offset must be 0 or greater', 'INVALID_OFFSET');
    }

    // Build filters object
    const filters: {
      startDate?: Date;
      endDate?: Date;
      entity?: string;
      syncStatus?: SyncStatus;
      integrationName?: string;
      parentOnly?: boolean;
      limit?: number;
      offset?: number;
    } = {};

    if (parsedStartDate) filters.startDate = parsedStartDate;
    if (parsedEndDate) filters.endDate = parsedEndDate;
    if (entity) filters.entity = entity as string;
    if (syncStatus) filters.syncStatus = syncStatus as SyncStatus;
    if (integrationName) filters.integrationName = integrationName as string;
    if (parentOnly !== undefined) filters.parentOnly = parentOnly === 'true';
    filters.limit = parsedLimit;
    filters.offset = parsedOffset;

    // Get integration logs
    const result = await getIntegrationLogs(companyId, userId, filters);

    logger.info('Integration logs retrieved successfully', {
      userId,
      companyId,
      filters,
      totalCount: result.pagination.totalCount,
      returnedCount: result.integrationLogs.length,
    });

    res.status(200).json({
      success: true,
      message: 'Integration logs retrieved successfully',
      data: result.integrationLogs,
      pagination: result.pagination,
      filters: result.filters,
    });
  } catch (error: unknown) {
    logger.error('Failed to retrieve integration logs', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.id,
      companyId: req.params['companyId'],
      query: req.query,
    });
    next(error);
  }
};

/**
 * Controller: Get detailed integration log by ID
 * Returns comprehensive information about a specific integration log
 *
 * @example
 * GET /api/v1/integration-logs/{logId}
 */
export const getIntegrationLogDetailsController = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { logId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
    }

    if (!logId) {
      throw new BadRequestError('Log ID is required', 'LOG_ID_REQUIRED');
    }

    // Get integration log with related data
    const integrationLog = await prisma.integrationLog.findFirst({
      where: {
        Id: logId,
        Company: {
          UserId: userId,
        },
      },
      orderBy: {
        CreatedAt: 'desc',
      },
    });

    if (!integrationLog) {
      throw new BadRequestError('Integration log not found or access denied', 'LOG_NOT_FOUND');
    }

    logger.info('Integration log details retrieved successfully', {
      userId,
      logId,
      entity: integrationLog.Entity,
      syncStatus: integrationLog.SyncStatus,
    });

    res.status(200).json({
      success: true,
      message: 'Integration log details retrieved successfully',
      data: integrationLog,
    });
  } catch (error: unknown) {
    logger.error('Failed to retrieve integration log details', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.id,
      logId: req.params['logId'],
    });
    next(error);
  }
};

/**
 * Controller: Retry a failed integration operation
 * Initiates a retry for a failed integration log
 *
 * @example
 * POST /api/v1/integration-logs/{logId}/retry
 */
export const retryIntegrationLogController = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { logId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
    }

    if (!logId) {
      throw new BadRequestError('Log ID is required', 'LOG_ID_REQUIRED');
    }

    // Retry the integration log
    const retriedLog = await retryIntegrationLog(logId, userId);

    logger.info('Integration log retry initiated successfully', {
      userId,
      logId,
      retryCount: retriedLog.RetryCount,
      maxRetries: retriedLog.MaxRetries,
    });

    res.status(200).json({
      success: true,
      message: `Retry initiated successfully (attempt ${retriedLog.RetryCount}/${retriedLog.MaxRetries})`,
      data: {
        id: retriedLog.Id,
        syncStatus: retriedLog.SyncStatus,
        retryCount: retriedLog.RetryCount,
        maxRetries: retriedLog.MaxRetries,
        lastRetryAt: retriedLog.LastRetryAt,
        message: retriedLog.Message,
      },
    });
  } catch (error: unknown) {
    logger.error('Failed to retry integration log', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.id,
      logId: req.params.logId,
    });
    next(error);
  }
};

/**
 * Controller: Get integration statistics for a company
 * Returns aggregated statistics and analytics for integration operations
 *
 * @example
 * GET /api/v1/companies/{companyId}/integration-stats
 */
export const getIntegrationStatsController = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const { companyId } = req.params;
    const userId = req.user?.id;

    if (!userId) {
      throw new BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
    }

    if (!companyId) {
      throw new BadRequestError('Company ID is required', 'COMPANY_ID_REQUIRED');
    }

    // Parse optional date range
    const { startDate, endDate } = req.query;
    let dateRange: { startDate: Date; endDate: Date } | undefined;

    if (startDate && endDate) {
      const parsedStartDate = new Date(startDate as string);
      const parsedEndDate = new Date(endDate as string);

      if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
        throw new BadRequestError('Invalid date format', 'INVALID_DATE_FORMAT');
      }

      dateRange = {
        startDate: parsedStartDate,
        endDate: parsedEndDate,
      };
    }

    // Get integration statistics
    const stats = await getIntegrationLogStats(companyId, userId);

    logger.info('Integration statistics retrieved successfully', {
      userId,
      companyId,
      totalLogs: stats.totalLogs,
      dateRange,
    });

    res.status(200).json({
      success: true,
      message: 'Integration statistics retrieved successfully',
      data: stats,
    });
  } catch (error: unknown) {
    logger.error('Failed to retrieve integration statistics', {
      error: error instanceof Error ? error.message : 'Unknown error',
      userId: req.user?.id,
      companyId: req.params.companyId,
      query: req.query,
    });
    next(error);
  }
};
