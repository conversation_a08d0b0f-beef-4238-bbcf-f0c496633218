"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIntegrationLogs = void 0;
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const error_middleware_1 = require("@middlewares/error.middleware");
const getIntegrationLogs = async (companyId, userId, filters = {}) => {
    try {
        const { entity, syncStatus, integrationName, startDate, endDate, limit = 50, offset = 0, } = filters;
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const whereClause = {
            CompanyId: companyId,
        };
        if (entity)
            whereClause.Entity = entity;
        if (syncStatus)
            whereClause.SyncStatus = syncStatus;
        if (integrationName)
            whereClause.IntegrationName = integrationName;
        if (startDate || endDate) {
            whereClause.CreatedAt = {};
            if (startDate)
                whereClause.CreatedAt.gte = startDate;
            if (endDate)
                whereClause.CreatedAt.lte = endDate;
        }
        console.log("whereClause", whereClause);
        const [integrationLogs, totalCount] = await Promise.all([
            config_1.prisma.integrationLog.findMany({
                where: whereClause,
                orderBy: { CreatedAt: 'desc' },
                take: Math.min(limit, 100),
                skip: offset,
            }),
            config_1.prisma.integrationLog.count({
                where: whereClause,
            }),
        ]);
        logger_1.default.info('Integration logs retrieved successfully', {
            userId,
            companyId,
            totalCount,
            returnedCount: integrationLogs.length,
            filters,
        });
        return {
            integrationLogs,
            pagination: {
                totalCount,
                limit,
                offset,
                hasMore: offset + integrationLogs.length < totalCount,
            },
            filters,
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve integration logs', {
            companyId,
            userId,
            filters,
            error: errorMessage,
        });
        throw new error_middleware_1.InternalServerError(`Failed to retrieve integration logs: ${errorMessage}`, 'INTEGRATION_LOGS_RETRIEVAL_FAILED');
    }
};
exports.getIntegrationLogs = getIntegrationLogs;
//# sourceMappingURL=integrationLog.service.js.map