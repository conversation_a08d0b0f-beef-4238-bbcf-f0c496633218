"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.retryIntegrationLog = exports.getIntegrationLogStats = exports.updateIntegrationLog = exports.createIntegrationLog = exports.getIntegrationLogs = void 0;
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const error_middleware_1 = require("@middlewares/error.middleware");
const getIntegrationLogs = async (companyId, userId, filters = {}) => {
    try {
        const { entity, syncStatus, integrationName, startDate, endDate, limit = 50, offset = 0, } = filters;
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const whereClause = {
            CompanyId: companyId,
        };
        if (entity)
            whereClause.Entity = entity;
        if (syncStatus)
            whereClause.SyncStatus = syncStatus;
        if (integrationName)
            whereClause.IntegrationName = integrationName;
        if (startDate || endDate) {
            whereClause.CreatedAt = {};
            if (startDate)
                whereClause.CreatedAt.gte = startDate;
            if (endDate)
                whereClause.CreatedAt.lte = endDate;
        }
        const [integrationLogs, totalCount] = await Promise.all([
            config_1.prisma.integrationLog.findMany({
                where: whereClause,
                orderBy: { CreatedAt: 'desc' },
                take: Math.min(limit, 100),
                skip: offset,
            }),
            config_1.prisma.integrationLog.count({
                where: whereClause,
            }),
        ]);
        logger_1.default.info('Integration logs retrieved successfully', {
            userId,
            companyId,
            totalCount,
            returnedCount: integrationLogs.length,
            filters,
        });
        return {
            integrationLogs,
            pagination: {
                totalCount,
                limit,
                offset,
                hasMore: offset + integrationLogs.length < totalCount,
            },
            filters,
        };
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve integration logs', {
            companyId,
            userId,
            filters,
            error: errorMessage,
        });
        throw new error_middleware_1.InternalServerError(`Failed to retrieve integration logs: ${errorMessage}`, 'INTEGRATION_LOGS_RETRIEVAL_FAILED');
    }
};
exports.getIntegrationLogs = getIntegrationLogs;
const createIntegrationLog = async (data) => {
    try {
        const integrationLog = await config_1.prisma.integrationLog.create({
            data: {
                RequestId: data.requestId || `req_${Date.now()}`,
                CompanyId: data.companyId,
                userId: data.userId || null,
                ApiName: data.apiName,
                Method: data.method || null,
                ApiUrl: data.apiUrl || null,
                IntegrationName: data.integrationName || 'Xero',
                Entity: data.entity || null,
                TriggeredBy: data.triggeredBy || 'USER',
                SyncType: data.syncType || 'SINGLE_ENTITY',
                MaxRetries: data.maxRetries || 3,
                BatchId: data.batchId || null,
                ParentLogId: data.parentLogId || null,
                IsParentLog: data.isParentLog || false,
                TotalRecords: data.totalRecords || null,
                ApiRequest: data.apiRequest || null,
                SyncStatus: 'PENDING',
                StartedAt: new Date(),
            },
        });
        logger_1.default.info('Integration log created successfully', {
            id: integrationLog.Id,
            companyId: data.companyId,
            apiName: data.apiName,
            entity: data.entity,
        });
        return integrationLog;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to create integration log', {
            data,
            error: errorMessage,
        });
        throw new error_middleware_1.InternalServerError(`Failed to create integration log: ${errorMessage}`, 'INTEGRATION_LOG_CREATION_FAILED');
    }
};
exports.createIntegrationLog = createIntegrationLog;
const updateIntegrationLog = async (data) => {
    try {
        const updateData = {
            UpdatedAt: new Date(),
        };
        if (data.syncStatus !== undefined)
            updateData['SyncStatus'] = data.syncStatus;
        if (data.statusCode !== undefined)
            updateData['StatusCode'] = data.statusCode;
        if (data.duration !== undefined)
            updateData['Duration'] = data.duration;
        if (data.message !== undefined)
            updateData['Message'] = data.message;
        if (data.entityCount !== undefined)
            updateData['EntityCount'] = data.entityCount;
        if (data.successCount !== undefined)
            updateData['SuccessCount'] = data.successCount;
        if (data.failureCount !== undefined)
            updateData['FailureCount'] = data.failureCount;
        if (data.skippedCount !== undefined)
            updateData['SkippedCount'] = data.skippedCount;
        if (data.processedRecords !== undefined)
            updateData['ProcessedRecords'] = data.processedRecords;
        if (data.progressPercentage !== undefined)
            updateData['ProgressPercentage'] = data.progressPercentage;
        if (data.apiResponse !== undefined)
            updateData['ApiResponse'] = data.apiResponse;
        if (data.errorDetails !== undefined)
            updateData['ErrorDetails'] = data.errorDetails;
        if (data.syncSummary !== undefined)
            updateData['SyncSummary'] = data.syncSummary;
        if (data.completedAt !== undefined)
            updateData['CompletedAt'] = data.completedAt;
        const integrationLog = await config_1.prisma.integrationLog.update({
            where: { Id: data.id },
            data: updateData,
        });
        logger_1.default.info('Integration log updated successfully', {
            id: data.id,
            syncStatus: data.syncStatus,
            message: data.message,
        });
        return integrationLog;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to update integration log', {
            id: data.id,
            error: errorMessage,
        });
        throw new error_middleware_1.InternalServerError(`Failed to update integration log: ${errorMessage}`, 'INTEGRATION_LOG_UPDATE_FAILED');
    }
};
exports.updateIntegrationLog = updateIntegrationLog;
const getIntegrationLogStats = async (companyId, userId) => {
    try {
        const company = await config_1.prisma.company.findFirst({
            where: {
                Id: companyId,
                UserId: userId,
            },
        });
        if (!company) {
            throw new error_middleware_1.BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
        }
        const [totalLogs, successLogs, errorLogs, pendingLogs] = await Promise.all([
            config_1.prisma.integrationLog.count({
                where: { CompanyId: companyId },
            }),
            config_1.prisma.integrationLog.count({
                where: { CompanyId: companyId, SyncStatus: 'SUCCESS' },
            }),
            config_1.prisma.integrationLog.count({
                where: { CompanyId: companyId, SyncStatus: 'ERROR' },
            }),
            config_1.prisma.integrationLog.count({
                where: { CompanyId: companyId, SyncStatus: 'PENDING' },
            }),
        ]);
        const stats = {
            totalLogs,
            successLogs,
            errorLogs,
            pendingLogs,
            successRate: totalLogs > 0 ? (successLogs / totalLogs) * 100 : 0,
        };
        logger_1.default.info('Integration log statistics retrieved successfully', {
            userId,
            companyId,
            stats,
        });
        return stats;
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retrieve integration log statistics', {
            companyId,
            userId,
            error: errorMessage,
        });
        throw new error_middleware_1.InternalServerError(`Failed to retrieve integration log statistics: ${errorMessage}`, 'INTEGRATION_LOG_STATS_FAILED');
    }
};
exports.getIntegrationLogStats = getIntegrationLogStats;
const retryIntegrationLog = async (logId, userId) => {
    try {
        const integrationLog = await config_1.prisma.integrationLog.findFirst({
            where: {
                Id: logId,
                Company: {
                    UserId: userId,
                },
            },
        });
        if (!integrationLog) {
            throw new error_middleware_1.BadRequestError('Integration log not found or access denied', 'LOG_NOT_FOUND');
        }
        if (integrationLog.RetryCount >= integrationLog.MaxRetries) {
            throw new error_middleware_1.BadRequestError(`Maximum retry attempts (${integrationLog.MaxRetries}) exceeded`, 'MAX_RETRIES_EXCEEDED');
        }
        const updatedLog = await config_1.prisma.integrationLog.update({
            where: { Id: logId },
            data: {
                RetryCount: integrationLog.RetryCount + 1,
                LastRetryAt: new Date(),
                SyncStatus: 'PENDING',
                Message: `Retry attempt ${integrationLog.RetryCount + 1}/${integrationLog.MaxRetries}`,
                UpdatedAt: new Date(),
            },
        });
        logger_1.default.info('Integration log retry initiated', {
            userId,
            logId,
            retryCount: updatedLog.RetryCount,
            maxRetries: updatedLog.MaxRetries,
        });
        return updatedLog;
    }
    catch (error) {
        if (error instanceof error_middleware_1.BadRequestError) {
            throw error;
        }
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logger_1.default.error('Failed to retry integration log', {
            logId,
            userId,
            error: errorMessage,
        });
        throw new error_middleware_1.InternalServerError(`Failed to retry integration log: ${errorMessage}`, 'INTEGRATION_LOG_RETRY_FAILED');
    }
};
exports.retryIntegrationLog = retryIntegrationLog;
//# sourceMappingURL=integrationLog.service.js.map