export declare const XERO_MODULES: readonly ["Accounts", "Bank Transactions", "Bank Transfers", "Budgets", "Contacts", "Credit Notes", "Currencies", "Employees", "Expense Claims", "Invoices", "Journals", "Manual Journals", "Payments", "Tracking Categories", "Tax Rates", "Attachments", "Reports (P&L, BS, TB)"];
export type XeroModuleName = typeof XERO_MODULES[number];
export interface ModuleSyncStatus {
    id: string;
    companyId: string;
    moduleName: string;
    lastSyncTime: Date | null;
    createdAt: Date;
    updatedAt: Date;
}
export declare const getModuleSyncStatus: (companyId: string) => Promise<ModuleSyncStatus[]>;
export declare const createModule: (companyId: string) => Promise<void>;
//# sourceMappingURL=xeroModuleSync.service.d.ts.map