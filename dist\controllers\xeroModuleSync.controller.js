"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAvailableModulesController = void 0;
const xeroModuleSync_service_1 = require("@services/xeroModuleSync.service");
const response_1 = require("@utils/response");
const errorHandler_1 = require("@utils/errorHandler");
const error_middleware_1 = require("@middlewares/error.middleware");
const error_middleware_2 = require("@middlewares/error.middleware");
const getAvailableModulesHandler = async (_req, res, next) => {
    try {
        const { companyId } = _req.params;
        if (!companyId) {
            throw new error_middleware_2.BadRequestError('Company ID is required', 'MISSING_COMPANY_ID');
        }
        const responseData = await (0, xeroModuleSync_service_1.getModuleSyncStatus)(companyId);
        res.status(200).json((0, response_1.successResponse)('Available Xero modules retrieved successfully', responseData));
    }
    catch (error) {
        next((0, errorHandler_1.handleError)(error));
    }
};
exports.getAvailableModulesController = (0, error_middleware_1.asyncErrorHandler)(getAvailableModulesHandler);
//# sourceMappingURL=xeroModuleSync.controller.js.map