/**
 * @fileoverview
 * Entity Synchronization Service (Updated)
 * Handles Xero entity sync status, trigger, and history
 * with comprehensive IntegrationLog tracking
 */

import { prisma } from "@config/config";
import axiosClient from "@utils/axiosClient";
import logger from "@utils/logger";
import { v4 as uuidv4 } from "uuid";
import { BadRequestError, InternalServerError } from "@middlewares/error.middleware";
import {
  createIntegrationLog,
  updateIntegrationLog
} from "@services/integrationLog.service";
import { SyncType, SyncStatus } from "@prisma/client";
import { lambdaEntityUrlMap } from "@config/sync.config";

/**
 * Supported Xero entities
 */
export const SYNC_ENTITIES = [
  "Accounts",
  "BankTransactions",
  "BankTransfers",
  "Budgets",
  "Contacts",
  "CreditNotes",
  "Currencies",
  "Employees",
  "ExpenseClaims",
  "Invoices",
  "Journals",
  "ManualJournals",
  "Items",
  "Payments",
  "PurchaseOrders",
  "TaxRates",
  "TrackingCategories",
  "Attachments",
  "TrialBalance",
  "ProfitLoss",
  "BalanceSheet",
] as const;

export type SyncEntity = (typeof SYNC_ENTITIES)[number];

/**
 * Interface for sync trigger request
 */
export interface SyncTriggerRequest {
  entities: SyncEntity[];
  companyId: string;
  priority?: "HIGH" | "NORMAL" | "LOW";
  fullSync?: boolean;
}

/**
 * Get synchronization status for all entities
 */
export const getSyncStatus = async (userId: string, companyId: string) => {
  try {
    const company = await prisma.company.findFirst({
      where: { Id: companyId, UserId: userId },
    });

    if (!company) {
      throw new BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
    }

    const syncStatuses = await Promise.all(
      SYNC_ENTITIES.map(async (entity) => {
        const log = await prisma.integrationLog.findFirst({
          where: { CompanyId: companyId, Entity: entity },
          orderBy: { StartedAt: "desc" },
        });

        let status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED" | "NEVER_SYNCED" =
          "NEVER_SYNCED";

        if (log?.SyncStatus) {
          switch (log.SyncStatus) {
            case "PENDING":
            case "RETRYING":
              status = "PENDING";
              break;
            case "IN_PROGRESS":
              status = "IN_PROGRESS";
              break;
            case "SUCCESS":
            case "WARNING":
              status = "COMPLETED";
              break;
            case "ERROR":
            case "CANCELLED":
              status = "FAILED";
              break;
            default:
              status = "NEVER_SYNCED";
          }
        }

        return {
          entity,
          lastSync: log?.CompletedAt || log?.StartedAt || null,
          status,
          errorMessage: status === "FAILED" ? log?.Message || "" : "",
        };
      })
    );

    return {
      companyId,
      companyName: company.Name,
      connectionStatus: company.ConnectionStatus,
      entities: syncStatuses,
      lastUpdated: new Date(),
    };
  } catch (error) {
    logger.error("getSyncStatus failed", { userId, companyId, error });
    throw handleUnexpected(error, "SYNC_STATUS_RETRIEVAL_FAILED");
  }
};

/**
 * Trigger synchronization for specific entities
 * sending SQS messages (future) or direct Lambda calls (current)
 */
export const triggerEntitySync = async (
  userId: string,
  request: SyncTriggerRequest
) => {
  try {
    const { entities, companyId, priority = "NORMAL", fullSync = false } = request;

    const company = await prisma.company.findFirst({
      where: { Id: companyId, UserId: userId },
    });

    if (!company) {
      throw new BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
    }

    if (!company.XeroTenantId || company.ConnectionStatus !== "ACTIVE") {
      throw new BadRequestError(
        "Company does not have an active Xero connection",
        "NO_ACTIVE_XERO_CONNECTION"
      );
    }

    const triggerRecords: any[] = [];
    const MAX_RETRIES = 2;

    for (const entity of entities) {
      const requestId = uuidv4();
      const startedAt = new Date();
      const lambdaUrl = lambdaEntityUrlMap[entity];

      if (!lambdaUrl) {
        logger.warn(`No Lambda URL configured for entity: ${entity}`);
        triggerRecords.push({
          entity,
          status: "SKIPPED",
          reason: "No Lambda URL configured",
          requestId,
        });
        continue;
      }

      const payload = {
        entity,
        companyId,
        fullSync,
        priority,
        requestId,
        dumpToDatabase: true,
      };

      let success = false;
      let lastError: any = null;

      for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
        try {
          await axiosClient.post(lambdaUrl, payload);
          success = true;
          break;
        } catch (error: any) {
          lastError = error;
          logger.warn(`Attempt ${attempt} failed for entity ${entity}: ${error.message}`);
          // optional: wait briefly between retries
        }
      }

      if (success) {
        triggerRecords.push({
          entity,
          status: "TRIGGERED",
          requestId,
        });
        const isReportEntity = ["TrialBalance", "ProfitLoss", "BalanceSheet"].includes(entity);
        const entityName = isReportEntity ? "Reports (P&L, BS, TB)" : entity;

        await prisma.xeroModuleSync.update({
          where: { CompanyId_ModuleName: { CompanyId: companyId, ModuleName: entityName } },
          data: {
            LastSyncTime: new Date(), // Update the timestamp here
          },
        })
      } else {
        const completedAt = new Date();
        const duration = `${completedAt.getTime() - startedAt.getTime()}ms`;

        // Log the failed sync operation
        const integrationLog = await createIntegrationLog({
          companyId,
          userId,
          apiName: `Sync ${entity}`,
          method: "POST",
          apiUrl: lambdaUrl,
          integrationName: "Xero",
          entity,
          syncType: SyncType.SINGLE_ENTITY,
          triggeredBy: "USER",
          apiRequest: payload,
        });

        await updateIntegrationLog({
          id: integrationLog.Id,
          syncStatus: SyncStatus.ERROR,
          statusCode: "500",
          duration,
          message: `Failed to sync ${entity}`,
          apiResponse: lastError?.response?.data || lastError?.message || "Unknown error",
          errorDetails: {
            error: lastError?.message || "Unknown error",
            response: lastError?.response?.data,
            timestamp: new Date().toISOString(),
          },
        });

        triggerRecords.push({
          entity,
          status: "FAILED",
          requestId,
          error: lastError?.message || "Unknown error",
        });
      }
    }

    return {
      success: true,
      message: `Sync triggered for ${entities.length} entities`,
      data: {
        companyId,
        companyName: company.Name,
        triggerRecords,
        estimatedDuration: `${entities.length * 2}-${entities.length * 5} minutes`,
      },
    };
  } catch (error) {
    logger.error("triggerEntitySync failed", error);
    throw handleUnexpected(error, "ENTITY_SYNC_TRIGGER_FAILED");
  }
};


/**
 * Full sync for all entities
 */
export const triggerAllEntitiesSync = async (
  userId: string,
  companyId: string,
  options: { priority?: "HIGH" | "NORMAL" | "LOW"; fullSync?: boolean } = {}
) => {
  return triggerEntitySync(userId, {
    entities: [...SYNC_ENTITIES],
    companyId,
    ...(options.priority ? { priority: options.priority } : {}),
    ...(options.fullSync !== undefined ? { fullSync: options.fullSync } : {}),
  });
};

/**
 * Get sync history for a company
 */
export const getSyncHistory = async (
  userId: string,
  companyId: string,
  options: {
    limit?: number;
    offset?: number;
    entity?: SyncEntity;
    status?: string;
    dateFrom?: Date;
    dateTo?: Date;
  } = {}
) => {
  const { limit = 50, offset = 0, entity, status, dateFrom, dateTo } = options;

  const company = await prisma.company.findFirst({
    where: { Id: companyId, UserId: userId },
  });

  if (!company) {
    throw new BadRequestError("Company not found or access denied", "COMPANY_NOT_FOUND");
  }

  const where: any = { CompanyId: companyId };

  if (entity) {
    where.Entity = entity;
  }

  if (status) {
    const statusMap: Record<string, string> = {
      PENDING: "PENDING",
      IN_PROGRESS: "IN_PROGRESS",
      COMPLETED: "SUCCESS",
      FAILED: "ERROR",
    };
    where.SyncStatus = statusMap[status] || status;
  }

  if (dateFrom || dateTo) {
    where.StartedAt = {};
    if (dateFrom) where.StartedAt.gte = dateFrom;
    if (dateTo) where.StartedAt.lte = dateTo;
  }

  const [integrationLogs, totalCount] = await Promise.all([
    prisma.integrationLog.findMany({
      where,
      orderBy: { StartedAt: "desc" },
      take: limit,
      skip: offset,
    }),
    prisma.integrationLog.count({ where }),
  ]);

  const formatted = integrationLogs.map((log: any) => ({
    id: log.Id,
    entity: log.Entity,
    status: log.SyncStatus,
    errorMessage: log.Message,
    duration: log.Duration,
    createdAt: log.CreatedAt,
    completedAt: log.CompletedAt,
  }));

  return {
    companyId,
    companyName: company.Name,
    history: formatted,
    pagination: {
      totalCount,
      totalPages: Math.ceil(totalCount / limit),
      currentPage: Math.floor(offset / limit) + 1,
      limit,
      offset,
      hasNextPage: offset + limit < totalCount,
      hasPreviousPage: offset > 0,
    },
  };
};

/**
 * Utility to consistently handle unexpected errors
 */
const handleUnexpected = (error: unknown, code: string) => {
  if (error instanceof BadRequestError) return error;
  logger.error(`Unexpected error: ${error instanceof Error ? error.message : error}`);
  return new InternalServerError(
    error instanceof Error ? error.message : "Unknown error",
    code
  );
};
