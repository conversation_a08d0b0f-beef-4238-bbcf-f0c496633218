# IntegrationLog Update Summary

## Overview

The `IntegrationLog` model has been significantly enhanced to replace the old `ApiLog` and `SyncLog` tables, providing comprehensive logging capabilities for both single entity sync and multiple entity sync operations.

## Key Changes

### 1. Database Schema Updates

#### Removed Tables
- ✅ `ApiLog` table - Dropped with 594 existing records
- ✅ `SyncLog` table - Dropped with 77 existing records

#### Enhanced IntegrationLog Model
- **New Fields Added:**
  - `EntityCount`, `SuccessCount`, `FailureCount`, `SkippedCount` - For batch operation tracking
  - `SyncType` - Enum to distinguish sync operation types
  - `BatchId`, `ParentLogId`, `IsParentLog` - For parent-child log relationships
  - `TotalRecords`, `ProcessedRecords`, `ProgressPercentage` - For progress tracking
  - `SyncSummary` - JSON field for comprehensive operation summaries

#### New Enums
```prisma
enum SyncType {
  SINGLE_ENTITY     // Single entity sync (e.g., just Invoices)
  MULTIPLE_ENTITY   // Multiple entity sync (e.g., Invoices + Payments)
  FULL_SYNC         // Complete sync of all entities
  INCREMENTAL_SYNC  // Incremental sync based on last sync date
}
```

#### Performance Indexes
- `CompanyId + SyncStatus` - For filtering by company and status
- `RequestId` - For tracking related operations
- `BatchId` - For batch operation queries
- `ParentLogId` - For parent-child relationships
- `CreatedAt` - For time-based queries

### 2. Service Layer Enhancements

#### New IntegrationLog Service (`src/services/integrationLog.service.ts`)

**Core Functions:**
- `createIntegrationLog()` - Create new log entries
- `updateIntegrationLog()` - Update existing logs
- `getIntegrationLogs()` - Query logs with filtering and pagination
- `retryIntegrationLog()` - Retry failed operations
- `getIntegrationLogStats()` - Get comprehensive statistics

**Batch Operation Functions:**
- `createBatchSyncLog()` - Create parent log for batch operations
- `createChildSyncLogs()` - Create child logs for individual entities
- `updateBatchSyncProgress()` - Update batch operation progress

## Usage Patterns

### 1. Single Entity Sync

```typescript
// Create initial log
const syncLog = await createIntegrationLog({
  companyId: 'company-uuid',
  userId: 'user-uuid',
  apiName: 'Invoices',
  entity: 'Invoices',
  syncType: SyncType.SINGLE_ENTITY,
  triggeredBy: 'USER',
  totalRecords: 150,
});

// Update progress
await updateIntegrationLog({
  id: syncLog.Id,
  syncStatus: SyncStatus.IN_PROGRESS,
  processedRecords: 75,
  progressPercentage: 50,
});

// Complete sync
await updateIntegrationLog({
  id: syncLog.Id,
  syncStatus: SyncStatus.SUCCESS,
  successCount: 150,
  message: 'Successfully synced 150 invoices',
});
```

### 2. Multiple Entity Batch Sync

```typescript
const entities = ['Accounts', 'Contacts', 'Invoices'];

// Create parent log
const { parentLog, batchId } = await createBatchSyncLog(
  companyId,
  userId,
  entities,
  'USER'
);

// Create child logs
const childLogs = await createChildSyncLogs(
  parentLog.Id,
  batchId,
  companyId,
  userId,
  entities
);

// Process each entity and update child logs
for (const childLog of childLogs) {
  await updateIntegrationLog({
    id: childLog.Id,
    syncStatus: SyncStatus.SUCCESS,
    successCount: 100,
  });
}

// Update parent with summary
await updateBatchSyncProgress(parentLog.Id, {
  totalEntities: 3,
  successfulEntities: ['Accounts', 'Contacts', 'Invoices'],
  failedEntities: [],
  skippedEntities: [],
  totalRecords: 300,
  processedRecords: 300,
  errors: [],
});
```

### 3. Querying Logs

```typescript
// Get all logs with pagination
const logs = await getIntegrationLogs(companyId, userId, {
  limit: 50,
  offset: 0,
});

// Get failed logs only
const failedLogs = await getIntegrationLogs(companyId, userId, {
  syncStatus: SyncStatus.ERROR,
});

// Get batch operations only
const batchLogs = await getIntegrationLogs(companyId, userId, {
  syncType: SyncType.MULTIPLE_ENTITY,
  parentOnly: true,
});

// Get logs for specific entity
const invoiceLogs = await getIntegrationLogs(companyId, userId, {
  entity: 'Invoices',
});
```

### 4. Statistics and Monitoring

```typescript
const stats = await getIntegrationLogStats(companyId, userId);

console.log('Status distribution:', stats.statusDistribution);
console.log('Entity distribution:', stats.entityDistribution);
console.log('Sync type distribution:', stats.syncTypeDistribution);
```

## Migration Impact

### Data Loss
- **594 ApiLog records** were dropped during migration
- **77 SyncLog records** were dropped during migration
- This was necessary to consolidate into the new unified structure

### Code Updates Required

#### 1. Update Import Statements
```typescript
// Old
import { logApiCall } from '@utils/logApiCall';
import { createSyncLog, updateSyncLog } from '@services/syncLog.service';

// New
import {
  createIntegrationLog,
  updateIntegrationLog,
} from '@services/integrationLog.service';
```

#### 2. Update Service Calls
```typescript
// Old API logging
await logApiCall({
  companyId,
  userId,
  method: 'GET',
  apiUrl: '/invoices',
  status: '200',
  integrationName: 'Xero',
  apiName: 'Invoices',
});

// New integration logging
await createIntegrationLog({
  companyId,
  userId,
  method: 'GET',
  apiUrl: '/invoices',
  integrationName: 'Xero',
  apiName: 'Invoices',
  entity: 'Invoices',
  syncType: SyncType.SINGLE_ENTITY,
});
```

## Benefits

### 1. Unified Logging
- Single table for all API and sync operations
- Consistent data structure and querying
- Reduced complexity in log management

### 2. Enhanced Batch Support
- Parent-child relationships for batch operations
- Comprehensive progress tracking
- Detailed success/failure statistics

### 3. Better Performance
- Optimized indexes for common query patterns
- Efficient pagination and filtering
- Reduced database overhead

### 4. Improved Monitoring
- Rich statistics and analytics
- Progress tracking for long-running operations
- Comprehensive error details and retry logic

## Next Steps

1. **Update existing sync services** to use the new IntegrationLog service
2. **Implement batch sync operations** using the parent-child log pattern
3. **Update monitoring dashboards** to use the new statistics endpoints
4. **Test retry functionality** for failed operations
5. **Consider implementing** automated cleanup for old logs

## Example Files

- **Service Implementation:** `src/services/integrationLog.service.ts`
- **Usage Examples:** `src/examples/integrationLogUsage.example.ts`
- **Migration File:** `prisma/migrations/20250708065942_update_integration_log_for_batch_sync/migration.sql`

## Support

For questions or issues with the new IntegrationLog system, please refer to:
- The comprehensive service documentation in `integrationLog.service.ts`
- Usage examples in `integrationLogUsage.example.ts`
- This summary document for migration guidance
