/**
 * @fileoverview Integration Log Service
 * @description Comprehensive service for logging API calls and sync operations.
 * Supports both single entity sync and multiple entity sync scenarios with
 * detailed tracking, retry logic, and progress monitoring.
 *
 * Features:
 * - Single entity sync logging
 * - Multiple entity batch sync logging
 * - Parent-child log relationships for batch operations
 * - Progress tracking for long-running operations
 * - Retry mechanism with exponential backoff
 * - Comprehensive error handling and diagnostics
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-07-08
 */

import { prisma } from '@config/config';
import { SyncStatus } from '@prisma/client';
import logger from '@utils/logger';
import { BadRequestError, InternalServerError } from '@middlewares/error.middleware';

/**
 * Interface for creating a new integration log entry
 */
export interface CreateIntegrationLogRequest {
  requestId?: string;
  companyId: string;
  userId?: string;
  apiName: string;
  method?: string;
  apiUrl?: string;
  integrationName?: string;
  entity?: string;
  triggeredBy?: 'USER' | 'SYSTEM';
  syncType?: 'SINGLE_ENTITY' | 'MULTIPLE_ENTITY' | 'FULL_SYNC';
  maxRetries?: number;
  batchId?: string;
  parentLogId?: string;
  isParentLog?: boolean;
  totalRecords?: number;
  apiRequest?: any;
}

/**
 * Interface for updating integration log
 */
export interface UpdateIntegrationLogRequest {
  id: string;
  syncStatus?: SyncStatus;
  statusCode?: string;
  duration?: string;
  message?: string;
  entityCount?: number;
  successCount?: number;
  failureCount?: number;
  skippedCount?: number;
  processedRecords?: number;
  progressPercentage?: number;
  apiResponse?: any;
  errorDetails?: any;
  syncSummary?: any;
  completedAt?: Date;
}

/**
 * Interface for batch sync summary
 */
export interface BatchSyncSummary {
  totalEntities: number;
  successfulEntities: string[];
  failedEntities: string[];
  skippedEntities: string[];
  totalRecords: number;
  processedRecords: number;
  errors: Array<{
    entity: string;
    error: string;
    details?: any;
  }>;
}


/**
 * Get integration logs with filtering and pagination
 * @param companyId - Company ID
 * @param userId - User ID for authorization
 * @param filters - Filter options
 * @returns Promise with paginated integration logs
 */
export const getIntegrationLogs = async (
  companyId: string,
  userId: string,
  filters: {
    entity?: string;
    syncStatus?: SyncStatus;
    integrationName?: string;
    startDate?: Date;
    endDate?: Date;
    batchId?: string;
    parentOnly?: boolean;
    limit?: number;
    offset?: number;
  } = {}
) => {
  try {
    const {
      entity,
      syncStatus,
      integrationName,
      startDate,
      endDate,
      limit = 50,
      offset = 0,
    } = filters;

    // Verify company ownership
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
      },
    });

    if (!company) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    // Build where clause
    const whereClause: any = {
      CompanyId: companyId,
    };

    if (entity) whereClause.Entity = entity;
    if (syncStatus) whereClause.SyncStatus = syncStatus;
    if (integrationName) whereClause.IntegrationName = integrationName;

    if (startDate || endDate) {
      whereClause.CreatedAt = {};
      if (startDate) whereClause.CreatedAt.gte = startDate;
      if (endDate) whereClause.CreatedAt.lte = endDate;
    }

    // Execute queries
    const [integrationLogs, totalCount] = await Promise.all([
      prisma.integrationLog.findMany({
        where: whereClause,
        orderBy: { CreatedAt: 'desc' },
        take: Math.min(limit, 100), // Cap at 100 for performance
        skip: offset,

      }),
      prisma.integrationLog.count({
        where: whereClause,
      }),
    ]);

    logger.info('Integration logs retrieved successfully', {
      userId,
      companyId,
      totalCount,
      returnedCount: integrationLogs.length,
      filters,
    });

    return {
      integrationLogs,
      pagination: {
        totalCount,
        limit,
        offset,
        hasMore: offset + integrationLogs.length < totalCount,
      },
      filters,
    };
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retrieve integration logs', {
      companyId,
      userId,
      filters,
      error: errorMessage,
    });
    throw new InternalServerError(
      `Failed to retrieve integration logs: ${errorMessage}`,
      'INTEGRATION_LOGS_RETRIEVAL_FAILED'
    );
  }
};

/**
 * Create a new integration log entry
 * @param data - Integration log creation data
 * @returns Promise with created integration log
 */
export const createIntegrationLog = async (data: CreateIntegrationLogRequest) => {
  try {
    const integrationLog = await prisma.integrationLog.create({
      data: {
        RequestId: data.requestId || `req_${Date.now()}`,
        CompanyId: data.companyId,
        userId: data.userId || null,
        ApiName: data.apiName,
        Method: data.method || null,
        ApiUrl: data.apiUrl || null,
        IntegrationName: data.integrationName || 'Xero',
        Entity: data.entity || null,
        TriggeredBy: data.triggeredBy || 'USER',
        SyncType: data.syncType || 'SINGLE_ENTITY',
        MaxRetries: data.maxRetries || 3,
        BatchId: data.batchId || null,
        ParentLogId: data.parentLogId || null,
        IsParentLog: data.isParentLog || false,
        TotalRecords: data.totalRecords || null,
        ApiRequest: data.apiRequest || null,
        SyncStatus: 'PENDING',
        StartedAt: new Date(),
      },
    });

    logger.info('Integration log created successfully', {
      id: integrationLog.Id,
      companyId: data.companyId,
      apiName: data.apiName,
      entity: data.entity,
    });

    return integrationLog;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to create integration log', {
      data,
      error: errorMessage,
    });
    throw new InternalServerError(
      `Failed to create integration log: ${errorMessage}`,
      'INTEGRATION_LOG_CREATION_FAILED'
    );
  }
};

/**
 * Update an existing integration log
 * @param data - Integration log update data
 * @returns Promise with updated integration log
 */
export const updateIntegrationLog = async (data: UpdateIntegrationLogRequest) => {
  try {
    const updateData: Record<string, any> = {
      UpdatedAt: new Date(),
    };

    // Only update fields that are provided
    if (data.syncStatus !== undefined) updateData['SyncStatus'] = data.syncStatus;
    if (data.statusCode !== undefined) updateData['StatusCode'] = data.statusCode;
    if (data.duration !== undefined) updateData['Duration'] = data.duration;
    if (data.message !== undefined) updateData['Message'] = data.message;
    if (data.entityCount !== undefined) updateData['EntityCount'] = data.entityCount;
    if (data.successCount !== undefined) updateData['SuccessCount'] = data.successCount;
    if (data.failureCount !== undefined) updateData['FailureCount'] = data.failureCount;
    if (data.skippedCount !== undefined) updateData['SkippedCount'] = data.skippedCount;
    if (data.processedRecords !== undefined) updateData['ProcessedRecords'] = data.processedRecords;
    if (data.progressPercentage !== undefined) updateData['ProgressPercentage'] = data.progressPercentage;
    if (data.apiResponse !== undefined) updateData['ApiResponse'] = data.apiResponse;
    if (data.errorDetails !== undefined) updateData['ErrorDetails'] = data.errorDetails;
    if (data.syncSummary !== undefined) updateData['SyncSummary'] = data.syncSummary;
    if (data.completedAt !== undefined) updateData['CompletedAt'] = data.completedAt;

    const integrationLog = await prisma.integrationLog.update({
      where: { Id: data.id },
      data: updateData,
    });

    logger.info('Integration log updated successfully', {
      id: data.id,
      syncStatus: data.syncStatus,
      message: data.message,
    });

    return integrationLog;
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to update integration log', {
      id: data.id,
      error: errorMessage,
    });
    throw new InternalServerError(
      `Failed to update integration log: ${errorMessage}`,
      'INTEGRATION_LOG_UPDATE_FAILED'
    );
  }
};

/**
 * Get integration log statistics for a company
 * @param companyId - Company ID
 * @param userId - User ID for authorization
 * @returns Promise with integration statistics
 */
export const getIntegrationLogStats = async (companyId: string, userId: string) => {
  try {
    // Verify company ownership
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
        UserId: userId,
      },
    });

    if (!company) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    // Get statistics
    const [totalLogs, successLogs, errorLogs, pendingLogs] = await Promise.all([
      prisma.integrationLog.count({
        where: { CompanyId: companyId },
      }),
      prisma.integrationLog.count({
        where: { CompanyId: companyId, SyncStatus: 'SUCCESS' },
      }),
      prisma.integrationLog.count({
        where: { CompanyId: companyId, SyncStatus: 'ERROR' },
      }),
      prisma.integrationLog.count({
        where: { CompanyId: companyId, SyncStatus: 'PENDING' },
      }),
    ]);

    const stats = {
      totalLogs,
      successLogs,
      errorLogs,
      pendingLogs,
      successRate: totalLogs > 0 ? (successLogs / totalLogs) * 100 : 0,
    };

    logger.info('Integration log statistics retrieved successfully', {
      userId,
      companyId,
      stats,
    });

    return stats;
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retrieve integration log statistics', {
      companyId,
      userId,
      error: errorMessage,
    });
    throw new InternalServerError(
      `Failed to retrieve integration log statistics: ${errorMessage}`,
      'INTEGRATION_LOG_STATS_FAILED'
    );
  }
};

/**
 * Retry a failed integration log
 * @param logId - Integration log ID
 * @param userId - User ID for authorization
 * @returns Promise with updated integration log
 */
export const retryIntegrationLog = async (logId: string, userId: string) => {
  try {
    // Get the integration log with company verification
    const integrationLog = await prisma.integrationLog.findFirst({
      where: {
        Id: logId,
        Company: {
          UserId: userId,
        },
      },
    });

    if (!integrationLog) {
      throw new BadRequestError('Integration log not found or access denied', 'LOG_NOT_FOUND');
    }

    // Check if retry is allowed
    if (integrationLog.RetryCount >= integrationLog.MaxRetries) {
      throw new BadRequestError(
        `Maximum retry attempts (${integrationLog.MaxRetries}) exceeded`,
        'MAX_RETRIES_EXCEEDED'
      );
    }

    // Update the log for retry
    const updatedLog = await prisma.integrationLog.update({
      where: { Id: logId },
      data: {
        RetryCount: integrationLog.RetryCount + 1,
        LastRetryAt: new Date(),
        SyncStatus: 'PENDING',
        Message: `Retry attempt ${integrationLog.RetryCount + 1}/${integrationLog.MaxRetries}`,
        UpdatedAt: new Date(),
      },
    });

    logger.info('Integration log retry initiated', {
      userId,
      logId,
      retryCount: updatedLog.RetryCount,
      maxRetries: updatedLog.MaxRetries,
    });

    return updatedLog;
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retry integration log', {
      logId,
      userId,
      error: errorMessage,
    });
    throw new InternalServerError(
      `Failed to retry integration log: ${errorMessage}`,
      'INTEGRATION_LOG_RETRY_FAILED'
    );
  }
};
