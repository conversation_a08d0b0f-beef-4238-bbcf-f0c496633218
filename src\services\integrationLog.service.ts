/**
 * @fileoverview Integration Log Service
 * @description Comprehensive service for logging API calls and sync operations.
 * Supports both single entity sync and multiple entity sync scenarios with
 * detailed tracking, retry logic, and progress monitoring.
 *
 * Features:
 * - Single entity sync logging
 * - Multiple entity batch sync logging
 * - Parent-child log relationships for batch operations
 * - Progress tracking for long-running operations
 * - Retry mechanism with exponential backoff
 * - Comprehensive error handling and diagnostics
 *
 * <AUTHOR> Development Team
 * @version 2.0.0
 * @since 2024-07-08
 */

import { prisma } from '@config/config';
import { SyncStatus } from '@prisma/client';
import logger from '@utils/logger';
import { BadRequestError, InternalServerError } from '@middlewares/error.middleware';

/**
 * Interface for creating a new integration log entry
 */
export interface CreateIntegrationLogRequest {
  requestId?: string;
  companyId: string;
  userId?: string;
  apiName: string;
  method?: string;
  apiUrl?: string;
  integrationName?: string;
  entity?: string;
  triggeredBy?: 'USER' | 'SYSTEM';
  maxRetries?: number;
  batchId?: string;
  parentLogId?: string;
  isParentLog?: boolean;
  totalRecords?: number;
  apiRequest?: any;
}

/**
 * Interface for updating integration log
 */
export interface UpdateIntegrationLogRequest {
  id: string;
  syncStatus?: SyncStatus;
  statusCode?: string;
  duration?: string;
  message?: string;
  entityCount?: number;
  successCount?: number;
  failureCount?: number;
  skippedCount?: number;
  processedRecords?: number;
  progressPercentage?: number;
  apiResponse?: any;
  errorDetails?: any;
  syncSummary?: any;
  completedAt?: Date;
}

/**
 * Interface for batch sync summary
 */
export interface BatchSyncSummary {
  totalEntities: number;
  successfulEntities: string[];
  failedEntities: string[];
  skippedEntities: string[];
  totalRecords: number;
  processedRecords: number;
  errors: Array<{
    entity: string;
    error: string;
    details?: any;
  }>;
}


/**
 * Get integration logs with filtering and pagination
 * @param companyId - Company ID
 * @param userId - User ID for authorization
 * @param filters - Filter options
 * @returns Promise with paginated integration logs
 */
export const getIntegrationLogs = async (
  companyId: string,
  userId: string,
  filters: {
    entity?: string;
    syncStatus?: SyncStatus;
    integrationName?: string;
    startDate?: Date;
    endDate?: Date;
    batchId?: string;
    parentOnly?: boolean;
    limit?: number;
    offset?: number;
  } = {}
) => {
  try {
    const {
      entity,
      syncStatus,
      integrationName,
      startDate,
      endDate,
      limit = 50,
      offset = 0,
    } = filters;

    // Verify company ownership
    const company = await prisma.company.findFirst({
      where: {
        Id: companyId,
      },
    });

    if (!company) {
      throw new BadRequestError('Company not found or access denied', 'COMPANY_NOT_FOUND');
    }

    // Build where clause
    const whereClause: any = {
      CompanyId: companyId,
    };

    if (entity) whereClause.Entity = entity;
    if (syncStatus) whereClause.SyncStatus = syncStatus;
    if (integrationName) whereClause.IntegrationName = integrationName;

    if (startDate || endDate) {
      whereClause.CreatedAt = {};
      if (startDate) whereClause.CreatedAt.gte = startDate;
      if (endDate) whereClause.CreatedAt.lte = endDate;
    }

    console.log("whereClause", whereClause);
    // Execute queries
    const [integrationLogs, totalCount] = await Promise.all([
      prisma.integrationLog.findMany({
        where: whereClause,
        orderBy: { CreatedAt: 'desc' },
        take: Math.min(limit, 100), // Cap at 100 for performance
        skip: offset,

      }),
      prisma.integrationLog.count({
        where: whereClause,
      }),
    ]);

    logger.info('Integration logs retrieved successfully', {
      userId,
      companyId,
      totalCount,
      returnedCount: integrationLogs.length,
      filters,
    });

    return {
      integrationLogs,
      pagination: {
        totalCount,
        limit,
        offset,
        hasMore: offset + integrationLogs.length < totalCount,
      },
      filters,
    };
  } catch (error: unknown) {
    if (error instanceof BadRequestError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    logger.error('Failed to retrieve integration logs', {
      companyId,
      userId,
      filters,
      error: errorMessage,
    });
    throw new InternalServerError(
      `Failed to retrieve integration logs: ${errorMessage}`,
      'INTEGRATION_LOGS_RETRIEVAL_FAILED'
    );
  }
};
