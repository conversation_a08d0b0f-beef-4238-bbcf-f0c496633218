"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getIntegrationStatsController = exports.retryIntegrationLogController = exports.getIntegrationLogDetailsController = exports.getIntegrationLogsController = void 0;
const client_1 = require("@prisma/client");
const integrationLog_service_1 = require("@services/integrationLog.service");
const config_1 = require("@config/config");
const logger_1 = __importDefault(require("@utils/logger"));
const error_middleware_1 = require("@middlewares/error.middleware");
const getIntegrationLogsController = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            throw new error_middleware_1.BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
        }
        if (!companyId) {
            throw new error_middleware_1.BadRequestError('Company ID is required', 'COMPANY_ID_REQUIRED');
        }
        const { startDate, endDate, entity, syncStatus, syncType, integrationName, parentOnly, limit = '50', offset = '0', } = req.query;
        let parsedStartDate;
        let parsedEndDate;
        if (startDate) {
            parsedStartDate = new Date(startDate);
            if (isNaN(parsedStartDate.getTime())) {
                throw new error_middleware_1.BadRequestError('Invalid start date format', 'INVALID_START_DATE');
            }
        }
        if (endDate) {
            parsedEndDate = new Date(endDate);
            if (isNaN(parsedEndDate.getTime())) {
                throw new error_middleware_1.BadRequestError('Invalid end date format', 'INVALID_END_DATE');
            }
        }
        if (syncStatus && !Object.values(client_1.SyncStatus).includes(syncStatus)) {
            throw new error_middleware_1.BadRequestError('Invalid sync status', 'INVALID_SYNC_STATUS');
        }
        if (syncType && !Object.values(client_1.SyncType).includes(syncType)) {
            throw new error_middleware_1.BadRequestError('Invalid sync type', 'INVALID_SYNC_TYPE');
        }
        const parsedLimit = parseInt(limit, 10);
        const parsedOffset = parseInt(offset, 10);
        if (isNaN(parsedLimit) || parsedLimit < 1 || parsedLimit > 100) {
            throw new error_middleware_1.BadRequestError('Limit must be between 1 and 100', 'INVALID_LIMIT');
        }
        if (isNaN(parsedOffset) || parsedOffset < 0) {
            throw new error_middleware_1.BadRequestError('Offset must be 0 or greater', 'INVALID_OFFSET');
        }
        const filters = {};
        if (parsedStartDate)
            filters.startDate = parsedStartDate;
        if (parsedEndDate)
            filters.endDate = parsedEndDate;
        if (entity)
            filters.entity = entity;
        if (syncStatus)
            filters.syncStatus = syncStatus;
        if (integrationName)
            filters.integrationName = integrationName;
        if (parentOnly !== undefined)
            filters.parentOnly = parentOnly === 'true';
        filters.limit = parsedLimit;
        filters.offset = parsedOffset;
        const result = await (0, integrationLog_service_1.getIntegrationLogs)(companyId, userId, filters);
        logger_1.default.info('Integration logs retrieved successfully', {
            userId,
            companyId,
            filters,
            totalCount: result.pagination.totalCount,
            returnedCount: result.integrationLogs.length,
        });
        res.status(200).json({
            success: true,
            message: 'Integration logs retrieved successfully',
            data: result.integrationLogs,
            pagination: result.pagination,
            filters: result.filters,
        });
    }
    catch (error) {
        logger_1.default.error('Failed to retrieve integration logs', {
            error: error instanceof Error ? error.message : 'Unknown error',
            userId: req.user?.id,
            companyId: req.params.companyId,
            query: req.query,
        });
        next(error);
    }
};
exports.getIntegrationLogsController = getIntegrationLogsController;
const getIntegrationLogDetailsController = async (req, res, next) => {
    try {
        const { logId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            throw new error_middleware_1.BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
        }
        if (!logId) {
            throw new error_middleware_1.BadRequestError('Log ID is required', 'LOG_ID_REQUIRED');
        }
        const integrationLog = await config_1.prisma.integrationLog.findFirst({
            where: {
                Id: logId,
                Company: {
                    UserId: userId,
                },
            },
            orderBy: {
                CreatedAt: 'desc',
            },
        });
        if (!integrationLog) {
            throw new error_middleware_1.BadRequestError('Integration log not found or access denied', 'LOG_NOT_FOUND');
        }
        logger_1.default.info('Integration log details retrieved successfully', {
            userId,
            logId,
            entity: integrationLog.Entity,
            syncStatus: integrationLog.SyncStatus,
        });
        res.status(200).json({
            success: true,
            message: 'Integration log details retrieved successfully',
            data: integrationLog,
        });
    }
    catch (error) {
        logger_1.default.error('Failed to retrieve integration log details', {
            error: error instanceof Error ? error.message : 'Unknown error',
            userId: req.user?.id,
            logId: req.params.logId,
        });
        next(error);
    }
};
exports.getIntegrationLogDetailsController = getIntegrationLogDetailsController;
const retryIntegrationLogController = async (req, res, next) => {
    try {
        const { logId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            throw new error_middleware_1.BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
        }
        if (!logId) {
            throw new error_middleware_1.BadRequestError('Log ID is required', 'LOG_ID_REQUIRED');
        }
        const retriedLog = await (0, integrationLog_service_1.retryIntegrationLog)(logId, userId);
        logger_1.default.info('Integration log retry initiated successfully', {
            userId,
            logId,
            retryCount: retriedLog.RetryCount,
            maxRetries: retriedLog.MaxRetries,
        });
        res.status(200).json({
            success: true,
            message: `Retry initiated successfully (attempt ${retriedLog.RetryCount}/${retriedLog.MaxRetries})`,
            data: {
                id: retriedLog.Id,
                syncStatus: retriedLog.SyncStatus,
                retryCount: retriedLog.RetryCount,
                maxRetries: retriedLog.MaxRetries,
                lastRetryAt: retriedLog.LastRetryAt,
                message: retriedLog.Message,
            },
        });
    }
    catch (error) {
        logger_1.default.error('Failed to retry integration log', {
            error: error instanceof Error ? error.message : 'Unknown error',
            userId: req.user?.id,
            logId: req.params.logId,
        });
        next(error);
    }
};
exports.retryIntegrationLogController = retryIntegrationLogController;
const getIntegrationStatsController = async (req, res, next) => {
    try {
        const { companyId } = req.params;
        const userId = req.user?.id;
        if (!userId) {
            throw new error_middleware_1.BadRequestError('User not authenticated', 'USER_NOT_AUTHENTICATED');
        }
        if (!companyId) {
            throw new error_middleware_1.BadRequestError('Company ID is required', 'COMPANY_ID_REQUIRED');
        }
        const { startDate, endDate } = req.query;
        let dateRange;
        if (startDate && endDate) {
            const parsedStartDate = new Date(startDate);
            const parsedEndDate = new Date(endDate);
            if (isNaN(parsedStartDate.getTime()) || isNaN(parsedEndDate.getTime())) {
                throw new error_middleware_1.BadRequestError('Invalid date format', 'INVALID_DATE_FORMAT');
            }
            dateRange = {
                startDate: parsedStartDate,
                endDate: parsedEndDate,
            };
        }
        const stats = await (0, integrationLog_service_1.getIntegrationLogStats)(companyId, userId);
        logger_1.default.info('Integration statistics retrieved successfully', {
            userId,
            companyId,
            totalLogs: stats.totalLogs,
            dateRange,
        });
        res.status(200).json({
            success: true,
            message: 'Integration statistics retrieved successfully',
            data: stats,
        });
    }
    catch (error) {
        logger_1.default.error('Failed to retrieve integration statistics', {
            error: error instanceof Error ? error.message : 'Unknown error',
            userId: req.user?.id,
            companyId: req.params.companyId,
            query: req.query,
        });
        next(error);
    }
};
exports.getIntegrationStatsController = getIntegrationStatsController;
//# sourceMappingURL=integrationLog.controller.js.map