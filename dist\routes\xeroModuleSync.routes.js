"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const xeroModuleSync_controller_1 = require("@controllers/xeroModuleSync.controller");
const auth_middleware_1 = require("@middlewares/auth.middleware");
const router = (0, express_1.Router)();
router.get('/modules/:companyId', auth_middleware_1.authenticate, xeroModuleSync_controller_1.getAvailableModulesController);
exports.default = router;
//# sourceMappingURL=xeroModuleSync.routes.js.map