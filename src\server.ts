import 'tsconfig-paths/register';
import cluster from 'cluster';
import os from 'os';
import { Server } from 'http';
import app from './app';
import logger from './utils/logger';
import { config, prisma } from '@config/config';

/**
 * Graceful shutdown handler
 */
const gracefulShutdown = async (server: Server, signal: string) => {
  logger.info(`Received ${signal}. Starting graceful shutdown...`);

  // Stop accepting new connections
  server.close(async (err: any) => {
    if (err) {
      logger.error('Error during server shutdown:', err);
      process.exit(1);
    }

    try {
      // Close database connections
      await prisma.$disconnect();
      logger.info('Database connections closed');

      // Perform any other cleanup tasks here
      logger.info('Graceful shutdown completed');
      process.exit(0);
    } catch (error) {
      logger.error('Error during graceful shutdown:', error);
      process.exit(1);
    }
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    logger.error('Forced shutdown after timeout');
    process.exit(1);
  }, 30000);
};

/**
 * Start server worker
 */
const startServer = () => {
  const server = app.listen(config.PORT, config.HOST, () => {
    logger.info(`🚀 Worker ${process.pid} running on http://${config.HOST}:${config.PORT}`);
    logger.info(`Environment: ${config.NODE_ENV}`);
    logger.info(
      `API Base URL: http://${config.HOST}:${config.PORT}${config.API.PREFIX}/${config.API.VERSION}`
    );
  });

  // Handle server errors
  server.on('error', (error: NodeJS.ErrnoException) => {
    if (error.syscall !== 'listen') {
      throw error;
    }

    const bind = typeof config.PORT === 'string' ? `Pipe ${config.PORT}` : `Port ${config.PORT}`;

    switch (error.code) {
      case 'EACCES':
        logger.error(`${bind} requires elevated privileges`);
        process.exit(1);
      case 'EADDRINUSE':
        logger.error(`${bind} is already in use`);
        process.exit(1);
      default:
        throw error;
    }
  });

  // Graceful shutdown handlers
  process.on('SIGTERM', () => gracefulShutdown(server, 'SIGTERM'));
  process.on('SIGINT', () => gracefulShutdown(server, 'SIGINT'));

  // Handle uncaught exceptions
  process.on('uncaughtException', (error) => {
    logger.error('Uncaught Exception:', error);
    gracefulShutdown(server, 'UNCAUGHT_EXCEPTION');
  });

  // Handle unhandled promise rejections
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    gracefulShutdown(server, 'UNHANDLED_REJECTION');
  });

  return server;
};

// Determine if clustering should be enabled
const shouldUseCluster = config.IS_PRODUCTION && !process.env['DISABLE_CLUSTER'];
const numCPUs = os.cpus().length;

if (shouldUseCluster && cluster.isPrimary) {
  // Primary process - fork workers
  logger.info(`Primary process ${process.pid} is running`);
  logger.info(`Forking ${numCPUs} workers...`);

  // Fork workers equal to number of CPU cores
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  // Listen for dying workers and restart them
  cluster.on('exit', (worker, code, signal) => {
    logger.warn(`Worker ${worker.process.pid} died (${signal || code}). Restarting...`);
    cluster.fork();
  });

  // Handle cluster shutdown
  process.on('SIGTERM', () => {
    logger.info('Primary received SIGTERM, shutting down workers...');
    for (const id in cluster.workers) {
      cluster.workers[id]?.kill();
    }
  });
} else {
  // Worker process or single process mode
  startServer();
}
