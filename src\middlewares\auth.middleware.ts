import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { UnauthorizedError } from './error.middleware';
import { config } from '../config/config';
import logger from '@utils/logger';

// extend the default Express Request type to include a user property
export interface AuthRequest extends Request {
  user?: any;
}

/**
 * Authentication middleware to protect routes
 * Checks for a Bearer token in the Authorization header,
 * verifies it, and attaches the decoded payload to req.user.
 */
export const authenticate = (req: AuthRequest, _res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    logger.debug('Processing authentication header', {
      hasAuthHeader: !!authHeader,
      authHeaderPrefix: `${authHeader?.substring(0, 10)}...`,
    });

    // check if authorization header exists and starts with Bearer
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new UnauthorizedError('Authorization token is required', 'MISSING_TOKEN');
    }

    const token: string | undefined = authHeader.split(' ')[1];
    if (!token) {
      throw new UnauthorizedError('Authorization token is required', 'MISSING_TOKEN');
    }

    // verify and decode the token
    const decoded: any = jwt.verify(token, config.JWT.SECRET as string);
    const payload: any = {
      ...decoded,
      id: decoded.userId,
      userId: decoded.userId,
    };
    req.user = payload; // attach decoded user info to the request
    next();
  } catch (error) {
    if (error instanceof UnauthorizedError) {
      next(error);
    } else {
      next(new UnauthorizedError('Invalid or expired token', 'INVALID_TOKEN'));
    }
  }
};
