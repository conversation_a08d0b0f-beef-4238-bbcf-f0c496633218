import winston from 'winston';
type CustomLogger = winston.Logger & {
    stream: {
        write(message: string): void;
    };
    logRequest(req: {
        method: string;
        originalUrl: string;
        user?: {
            id?: string;
        };
        ip?: string;
        get(header: string): string | undefined;
    }, res: {
        statusCode: number;
    }, responseTime: number): void;
    logError(error: Error, context?: Record<string, unknown>): void;
    logSecurity(event: string, details: Record<string, unknown>): void;
};
declare const _default: CustomLogger;
export default _default;
//# sourceMappingURL=logger.d.ts.map